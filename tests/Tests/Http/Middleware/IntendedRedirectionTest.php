<?php

namespace Tests\Http\Middleware;

use AwardForce\Http\Middleware\IntendedRedirection;
use Illuminate\Http\Request;
use Tests\UnitTestCase;

final class IntendedRedirectionTest extends UnitTestCase
{
    public function testSetsIntendedUrl(): void
    {
        $request = app(Request::class);
        $request->merge(['redirect' => '/redirect/url']);

        $middleware = new IntendedRedirection();
        $middleware->handle($request, function () {
        });

        $this->assertTrue(session()->has('intended'));
    }
}
