<?php

namespace Tests\Modules\Entries\Commands;

use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Entries;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Bus\UpdateSubmittableAttachments;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use Carbon\Carbon;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Tests\IntegratedTestCase;

final class UpdateEntryAttachmentsTest extends IntegratedTestCase
{
    use DispatchesJobs;

    public function init()
    {
        $this->entries = app(Entries::class);
        $this->entry = $this->muffin(Entry::class);
        $this->file = $this->muffin(File::class);
        $this->file2 = $this->muffin(File::class);
        $this->tab = $this->muffin(Tab::class, ['type' => 'Attachments']);
        $this->tab2 = $this->muffin(Tab::class, ['type' => 'Attachments']);
        $attachment = $this->muffin(Attachment::class, [
            'submittable_id' => $this->entry->id,
            'tab_id' => $this->tab->id,
            'file_id' => $this->file->id,
            'order' => 1,
        ]);
        $attachment2 = $this->muffin(Attachment::class, [
            'submittable_id' => $this->entry->id,
            'tab_id' => $this->tab->id,
            'file_id' => $this->file2->id,
            'order' => 2,
        ]);
        $this->attachmentField = $this->muffin(Field::class, [
            'type' => 'text',
            'resource' => Field::RESOURCE_ATTACHMENTS,
            'tab_id' => $this->tab->id,
        ]);

        $this->entries->setSubmittableAttachments($this->entry->id, [$this->file->id => [(string) $this->attachmentField->slug => 'Test']]);
    }

    public function testUpdateEntryAttachmentFields(): void
    {
        $attachment = app(AttachmentRepository::class)->getBy('submittable_id', $this->entry->id)->first();

        $this->assertEquals('Test', $attachment->fieldValues[(string) $this->attachmentField->slug]);

        $this->dispatch(
            new UpdateSubmittableAttachments(
                Form::FORM_TYPE_ENTRY,
                $this->entry->id,
                [
                    $this->file->id => [
                        (string) $this->attachmentField->slug => 'Test 2',
                    ],
                ],
                Carbon::now()
            )
        );

        $attachment = app(AttachmentRepository::class)->getById($attachment->id);

        $this->assertEquals('Test 2', $attachment->fieldValues[(string) $this->attachmentField->slug]);
    }

    public function testUpdateEntryAttachmentOrder(): void
    {
        $attachment = app(AttachmentRepository::class)->getBy('submittable_id', $this->entry->id)->first();

        $this->assertEquals(1, $attachment->order);

        $this->dispatch(
            new UpdateSubmittableAttachments(
                Form::FORM_TYPE_ENTRY,
                $this->entry->id,
                [],
                Carbon::now(),
                2,
                null,
                $this->file->id
            )
        );

        $attachment = app(AttachmentRepository::class)->getById($attachment->id);

        $this->assertEquals(2, $attachment->order);
    }

    public function testUpdateEntryAttachmentTab(): void
    {
        $attachment = app(AttachmentRepository::class)->getBy('submittable_id', $this->entry->id)->first();

        $this->assertEquals($this->tab->id, $attachment->tabId);

        $this->dispatch(
            new UpdateSubmittableAttachments(
                Form::FORM_TYPE_ENTRY,
                $this->entry->id,
                [],
                Carbon::now(),
                null,
                $this->tab2->id,
                $this->file->id
            )
        );

        $attachment = app(AttachmentRepository::class)->getById($attachment->id);

        $this->assertEquals($this->tab2->id, $attachment->tabId);
    }
}
