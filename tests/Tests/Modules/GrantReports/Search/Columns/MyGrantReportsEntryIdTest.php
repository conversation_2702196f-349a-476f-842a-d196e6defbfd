<?php

namespace Tests\Modules\GrantReports\Search\Columns;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Search\Columns\MyGrantReportsEntryId;
use Tests\IntegratedTestCase;

final class MyGrantReportsEntryIdTest extends IntegratedTestCase
{
    public function testItDisplaysTheLocalEntryID(): void
    {
        $form = FormSelector::get();
        $form->settings = FormSettings::create(['displayId' => true]);
        $form->save();
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => ($entry = $this->muffin(Entry::class))->id]);

        $value = (new MyGrantReportsEntryId)->html($grantReport);

        $this->assertSame(local_id($entry), $value);
    }
}
