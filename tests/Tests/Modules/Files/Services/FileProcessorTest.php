<?php

namespace Tests\Modules\Files\Services;

use AwardForce\Library\Exceptions\FailedVirusScan;
use AwardForce\Library\Filesystem\Courier;
use AwardForce\Library\Filesystem\Inspector;
use AwardForce\Library\Filesystem\Storage;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\AppFileProcessor;
use AwardForce\Modules\Files\Services\FileProcessor;
use AwardForce\Modules\Files\Services\ResourceValidator;
use Mockery as m;
use Platform\Events\EventDispatcher;
use Tests\IntegratedTestCase;

final class FileProcessorTest extends IntegratedTestCase
{
    use EventDispatcher;

    /** @var FileProcessor */
    private $processor;

    /** @var m\MockInterface|Storage */
    private $storage;

    /** @var m\MockInterface|Courier */
    private $courier;

    /** @var m\MockInterface|Inspector */
    private $inspector;

    public function init()
    {
        $this->storage = m::mock(Storage::class);
        $this->storage->shouldIgnoreMissing();

        $this->storage->shouldReceive('courier')->andReturn($this->courier = m::mock(Courier::class));
        $this->storage->shouldReceive('inspector')->andReturn($this->inspector = m::mock(Inspector::class));

        $this->inspector->shouldReceive('size')->andReturn(0)->byDefault();
        $this->inspector->shouldReceive('mime')->andReturn('')->byDefault();

        FileProcessor::forgetValidators();

        $this->processor = new AppFileProcessor($this->storage);
    }

    public function testFailWhenFileNotFound(): void
    {
        $file = $this->muffin(File::class);
        AppFileProcessor::getInstance()->process($file);

        // Check file is marked as rejected
        $file = $file->fresh();
        $this->assertEquals(File::STATUS_REJECTED, $file->status);
        $this->assertEquals(File::STATUS_REJECTED_NOT_FOUND, $file->statusMessage);
    }

    public function testChunkedFileIsJoined(): void
    {
        $this->markTestIncomplete('Chunked files need implementation');
    }

    public function testFileMetadataIsUpdated(): void
    {
        $file = $this->muffin(File::class, ['original' => 'original.jpg']);
        $this->courier->shouldReceive('copyToLocal')
            ->with($file->file, m::on(function ($file) {
                return file_extension($file) == 'jpg';
            }))->once()
            ->andReturn($localFile = 'local.jpg');

        $this->inspector->shouldReceive('size')->with($localFile)->once()->andReturn(1337);
        $this->inspector->shouldReceive('mime')->with($localFile)->once()->andReturn('image/jpg');

        // FYI, fails validation so both files deleted... but we can still test the metadata!
        $this->shouldReceiveLocalDelete($localFile);

        $this->processor->process($file);

        $file = $file->fresh();
        $this->assertEquals(1337, $file->size);
        $this->assertEquals('image/jpg', $file->mime);
    }

    public function testFileRejectedWithNoMatchingValidator(): void
    {
        $file = $this->muffin(File::class);
        $this->courier->shouldReceive('copyToLocal')->with($file->file, m::any())->once()->andReturn($localFile = 'local.jpg');

        // Validator rejects file
        $this->addValidator(false);

        // Both files removed
        $this->shouldReceiveLocalDelete($localFile);

        // Process
        $this->processor->process($file);

        // Check file is marked as rejected
        $file = $file->fresh();
        $this->assertEquals(File::STATUS_REJECTED, $file->status);
        $this->assertEquals(File::STATUS_REJECTED_NO_VALIDATOR, $file->statusMessage);
    }

    public function testFileRejectedByValidator(): void
    {
        $file = $this->muffin(File::class);
        $this->courier->shouldReceive('copyToLocal')->with($file->file, m::any())->once()->andReturn($localFile = 'local.jpg');

        $this->addValidator(false); // doesn't validate
        $this->addValidator(true, false); // validates but rejects

        $this->shouldReceiveLocalDelete($localFile);

        $this->processor->process($file);

        $file = $file->fresh();
        $this->assertEquals(File::STATUS_REJECTED, $file->status);
        $this->assertEquals(File::STATUS_REJECTED_VALIDATION_FAILED, $file->statusMessage);
    }

    public function testFileRejectedByVirusScanner(): void
    {
        $file = $this->muffin(File::class);
        $this->courier->shouldReceive('copyToLocal')->with($file->file, m::any())->once()->andReturn($localFile = 'local.jpg');
        $this->addValidator(true, true);

        // Fail virus scan
        $this->inspector->shouldReceive('scan')->with($localFile, m::any())->once()->andThrow(FailedVirusScan::class);

        // Both files removed (we don't want to leave a virus lying around!)
        $this->shouldReceiveLocalDelete($localFile);

        $this->processor->process($file);

        $file = $file->fresh();
        $this->assertEquals(File::STATUS_REJECTED, $file->status);
        $this->assertEquals(File::STATUS_REJECTED_VIRUS_FOUND, $file->statusMessage);
    }

    public function testFileMovedToFinalPathOnSuccess(): void
    {
        $file = $this->muffin(File::class, ['original' => 'original.jpg', 'status' => File::STATUS_PENDING]);
        $this->addValidator(true, true);

        $this->inspector->shouldReceive('scan')->with($localFile = 'local.jpg', m::any())->once();

        $this->courier->shouldReceive('copyToLocal')->with($file->file, m::any())->once()->andReturn($localFile);
        $this->courier->shouldReceive('wasChunked')->andReturn(false);
        $this->courier->shouldReceive('moveRemote')->with('local.jpg', m::any())->once();

        // Only local file should be deleted
        $this->shouldReceiveLocalDelete($localFile);

        $this->processor->process($file);

        $this->dispatch($file->releaseEvents());
        $this->assertEquals(File::STATUS_OK, $file->status);
    }

    public function testChunkedFileUploadedToFinalPathOnSuccess(): void
    {
        $file = $this->muffin(File::class, ['resource' => File::RESOURCE_SETTING, 'original' => 'original.jpg', 'status' => File::STATUS_PENDING]);
        $this->addValidator(true, true);

        $this->inspector->shouldReceive('scan')->with($localFile = 'local.jpg', m::any())->once();

        $this->courier->shouldReceive('copyToLocal')->with($file->file, m::any())->once()->andReturn($localFile);
        $this->courier->shouldReceive('wasChunked')->andReturn(true);
        $this->courier->shouldReceive('copyToRemote')->with('local.jpg', m::any())->once();
        $this->courier->shouldReceive('setAcl')->once();

        // Only local file should be deleted
        $this->shouldReceiveLocalDelete($localFile);

        $this->processor->process($file);

        $this->dispatch($file->releaseEvents());
        $file = $file->fresh();
        $this->assertEquals(File::STATUS_OK, $file->status);
    }

    private function addValidator($validates = false, $check = false): ResourceValidator
    {
        $validator = new class($validates, $check) extends ResourceValidator
        {
            private $validates;
            private $check;

            public function __construct($validates, $check)
            {
                $this->validates = $validates;
                $this->check = $check;
            }

            public function resource(): string
            {
            }

            public function allowedTypes(): array
            {
            }

            public function validates(File $file): bool
            {
                return is_callable($this->validates) ? ($this->validates)($file) : $this->validates;
            }

            public function check(File $file): bool
            {
                return is_callable($this->check) ? ($this->check)($file) : $this->check;
            }
        };

        FileProcessor::registerValidators([$validator]);

        return $validator;
    }

    private function shouldReceiveLocalDelete(string $file)
    {
        $this->courier->shouldReceive('removeLocal')->with($file)->andReturnNull();
    }
}
