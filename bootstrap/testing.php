<?php

require_once __DIR__.'/autoload.php';

putenv('APP_ENV=testing');
putenv('APP_DEBUG=true');

putenv('DB_TESTING_CONNECTION=testing');
putenv('PROVISIONING_DOMAIN=provisioning.awardforce.app');
putenv('AWARDFORCE_DOMAIN_SETUP=cr4ce.awardforce.app');
putenv('GOODGRANTS_DOMAIN_SETUP=cr4ce.goodgrants.app');
putenv('WHITE_LABEL_DOMAINS_AWARDFORCE=awardforce.app');
putenv('ROOT_PROVISIONING_DOMAINS=provisioning.awardforce.app');
putenv('IMGIX_DOMAIN_AU=example.net');

$dir = str_replace(' ', '\ ', __DIR__);
$envfile = $dir.'/../.env';
$artisan = $dir.'/../artisan';

if (! ($token = getenv('TEST_TOKEN'))) {
    passthru('php '.$artisan
        .' migrate --database=testing --env=testing --quiet');
    passthru('php '.$artisan.' seed:initial-data -n');
}

passthru('php '.$artisan.' es:indices:create event_logs_01');
if (function_exists('opcache_get_status') && is_array($optCache = opcache_get_status()) && array_key_exists('jit', $optCache)) {
    echo 'Running with '.($optCache['jit']['enabled'] ? 'JIT enabled' : 'JIT disabled')."\n";
} else {
    echo "Running without opcache and JIT\n";
}

// we need to use our own implementation of TestSuiteLoader in order to handle test files .test.php
class_alias('Tests\Factory\TestSuiteLoader', 'PHPUnit\Runner\TestSuiteLoader');
