import Allocation from '@/modules/funding/components/allocation/Allocation.vue';

import { expect } from 'chai';
import { makeAllocation } from '@/modules/funding/components/allocation/AllocationType';
import { shallowMount } from '@vue/test-utils';

const lang = { get: () => '' };

describe('Allocation', () => {
	it('shows allocation tags', () => {
		const allocation = {
			id: 999,
			slug: 'ABCDEF',
			fundId: 1,
			entryId: 4033,
			currency: 'EUR',
			name: 'Fund 2023',
			amount: '€ 987.00',
			rawAmount: 987,
			tags: ['tag1', 'tag2'],
			paid: '€ 0.00',
			created_at: '2023-04-03',
			grantEndDate: null,
		};

		const wrapper = shallowMount(Allocation, {
			provide: { lang },
			propsData: {
				value: makeAllocation(allocation),
				index: 1,
				locale: 'en_GB',
				allocationPermissions: {
					canViewPayments: true,
					canUpdateOrDelete: true,
					canCreate: true,
					canUpdate: true,
					canDelete: true,
				},
			},
		});

		expect(wrapper.html()).to.contain('<span class="label label-tag">tag1</span>');
		expect(wrapper.html()).to.contain('<span class="label label-tag">tag2</span>');
	});
});
