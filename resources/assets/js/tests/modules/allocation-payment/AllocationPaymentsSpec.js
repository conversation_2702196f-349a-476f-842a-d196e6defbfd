import actions from '@/lib/store/modules/allocation-payment/actions';
import AllocationPayments from '@/modules/allocation-payment/components/AllocationPayments.vue';
import { expect } from 'chai';
import { makeAllocationPayment } from '@/modules/allocation-payment/components/AllocationPaymentType';
import moment from 'moment-timezone';
import mutations from '@/lib/store/modules/allocation-payment/mutations';
import sinon from 'sinon';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

const lang = { get: () => '' };

const defaultAllocationPayment = {
	id: 1,
	slug: 'ABCDEF',
	amount: 123,
	allocation_id: 1,
};

const allocationPaymentsArray = [defaultAllocationPayment];
const allocation = {
	id: 1,
	slug: 'ABCDEF',
	amount: 123,
	currency: 'EUR',
	allocationPayments: allocationPaymentsArray,
};

const state = (options = {}) => ({
	allocation: allocation,
	editOnlyAllocationPayment: defaultAllocationPayment,
	modalIsOpen: false,
	...options,
});

const routes = {
	'allocation-payment.allocation.index': 'allocation-payments/allocations/{fundAllocation}',
	'allocation-payment.create': 'allocation-payments',
	'allocation-payment.update': 'allocation-payments/{allocationPayment}',
	'allocation-payment.delete': 'allocation-payments',
	'allocation-payment.comments.index': 'allocation-payments/{allocationPayment}/comments',
};

let store;

const localVue = createLocalVue();
localVue.use(Vuex);

let defaultAllocationPayments;
let fetchAllocationPaymentCommentsResponse;

describe('AllocationPayments', () => {
	beforeEach(() => {
		fetchAllocationPaymentCommentsResponse = Promise.resolve({
			data: {
				comments: [],
				tags: [],
				token: 'abcdefg',
			},
		});

		store = (options) =>
			new Vuex.Store({
				modules: {
					allocationPayments: {
						namespaced: true,
						state: state,
						actions: actions,
						mutations: mutations,
					},
				},
				...options,
			});

		defaultAllocationPayments = (options = {}) => {
			const defaultOptions = {
				provide: { lang },
				propsData: {
					routes: routes,
					locale: 'en-GB',
				},
				methods: {},
				store: store(),
				localVue,
			};

			return { ...defaultOptions, ...options };
		};
	});

	it('does not fetch allocation payments when is opened from an allocation overflow and does not exist in allocation object', (done) => {
		const backupAllocationPaymentSpy = sinon.spy();

		const allocationPayments = shallowMount(
			AllocationPayments,
			defaultAllocationPayments({
				methods: {
					backupAllocationPayment: backupAllocationPaymentSpy,
				},
				mocks: {
					$http: {
						get: () => fetchAllocationPaymentCommentsResponse,
					},
				},
			})
		);

		allocationPayments.setData({
			currency: 'EUR',
		});

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(0);

		allocationPayments.vm.$store.dispatch('allocationPayments/openModalWithAllocation', allocation);

		setTimeout(function () {
			expect(allocationPayments.vm.form.allocationPayments.length).to.equal(1);

			done();
		}, 200);
	});

	it('fetch allocation payments when is opened from an allocation overflow and does not exist in allocation object', (done) => {
		const fetchAllocationPaymentsResponse = Promise.resolve({
			data: allocationPaymentsArray,
		});

		const allocationPayments = shallowMount(
			AllocationPayments,
			defaultAllocationPayments({
				mocks: {
					$http: {
						get: () => fetchAllocationPaymentsResponse,
					},
				},
			})
		);

		allocationPayments.setData({
			currency: 'EUR',
			modeState: {
				isFetching() {
					return true;
				},
			},
		});

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(0);

		const allocation = {
			id: 1,
			slug: 'ABCDEF',
			amount: 123,
			currency: 'EUR',
			allocationPayments: [],
		};

		allocationPayments.vm.$store.dispatch('allocationPayments/openModalWithAllocation', allocation);

		setTimeout(function () {
			expect(allocationPayments.vm.form.allocationPayments.length).to.equal(1);

			done();
		}, 200);
	});

	it('formats amount properly', () => {
		const allocationPayments = shallowMount(AllocationPayments, defaultAllocationPayments());

		allocationPayments.setData({
			currency: 'EUR',
		});

		expect(allocationPayments.vm.formatAmount(100)).to.equal('€ 100.00');
	});

	it('closes properly', () => {
		const allocationPayments = shallowMount(AllocationPayments, defaultAllocationPayments());
		allocationPayments.setData({
			form: {
				allocationPayments: [
					makeAllocationPayment({
						id: 1,
						payment_method_id: null,
						status: 'scheduled',
						reference: '#123456',
						date_due: '2023-03-03',
						date_paid: null,
						amount: 10,
					}),
				],
			},
			allocationId: 1,
			allocationSlug: 'ABCDEF',
			currency: 'EUR',
			allocatedAmount: 123,
		});

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(1);
		expect(allocationPayments.vm.currency).to.equal('EUR');
		expect(allocationPayments.vm.allocatedAmount).to.equal(123);

		allocationPayments.vm.close();

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(0);
		expect(allocationPayments.vm.currency).to.null;
		expect(allocationPayments.vm.allocatedAmount).to.null;
	});

	it('fetches max allocation payment amount', () => {
		const allocationPayments = shallowMount(AllocationPayments, defaultAllocationPayments());
		allocationPayments.setData({
			form: {
				allocationPayments: [
					{ id: 1, amount: 100 },
					{ id: 2, amount: 200 },
				],
			},
			allocatedAmount: 1000,
			currency: 'EUR',
		});

		expect(allocationPayments.vm.getAllocationPaymentMaxAmount(0)).to.equal(800);
	});

	it('fetches default comment configuration', () => {
		const allocationPayments = shallowMount(AllocationPayments, defaultAllocationPayments());

		const defaultCommentConfiguration = allocationPayments.vm.getDefaultCommentConfiguration();
		expect(Object.prototype.hasOwnProperty.call(defaultCommentConfiguration, 'comments')).to.be.true;
		expect(Object.prototype.hasOwnProperty.call(defaultCommentConfiguration, 'tags')).to.be.true;
		expect(Object.prototype.hasOwnProperty.call(defaultCommentConfiguration, 'token')).to.be.true;
	});

	it('adds an allocation payment', () => {
		const allocationPayments = shallowMount(AllocationPayments, defaultAllocationPayments());

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(0);

		allocationPayments.vm.addAllocationPayment({
			id: 1,
			amount: 100,
			slug: 'ABCDEF',
		});

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(1);
	});

	it('select a payment schedule template', () => {
		const paymentScheduleTemplate = {
			slug: 'vDMOzMqv',
			payments: [
				{
					amount: '30',
					date_due: '1',
					reference: '123',
					date_option: 'payment_schedule_created',
					unit_option: 'days_after',
					amount_option: 'percentage',
					payment_method_id: 14,
				},
				{
					amount: '50',
					date_due: '2',
					reference: '456',
					date_option: 'allocation_created',
					unit_option: 'days_after',
					amount_option: 'percentage',
					payment_method_id: 17,
				},
				{
					amount: '20',
					date_due: '4',
					reference: '789',
					date_option: 'payment_schedule_created',
					unit_option: 'days_after',
					amount_option: 'percentage',
					payment_method_id: 16,
				},
			],
			name: 'Default percentages',
		};

		const allocationPayments = shallowMount(
			AllocationPayments,
			defaultAllocationPayments({
				propsData: {
					paymentScheduleTemplates: [paymentScheduleTemplate],
				},
			})
		);
		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(0);

		allocationPayments.vm.selectPaymentScheduleTemplate('name', paymentScheduleTemplate.slug);

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(3);
	});

	it('get date due from payment schedule template', () => {
		const paymentScheduleTemplate = {
			slug: 'vDMOzMqv',
			payments: [
				{
					date_due: '1',
					date_option: 'payment_schedule_created',
					unit_option: 'days_after',
				},
				{
					date_due: '2',
					date_option: 'allocation_created',
					unit_option: 'days_after',
				},
				{
					date_due: '4',
					date_option: 'grant_end_date',
					unit_option: 'days_after',
				},
			],
			created_at: '2023-03-01',
		};

		const allocationPayments = shallowMount(
			AllocationPayments,
			defaultAllocationPayments({
				propsData: {
					unitOptions: [
						{ key: 'days_before', interval: 'days' },
						{ key: 'days_after', interval: 'days' },
						{ key: 'weeks_before', interval: 'weeks' },
						{ key: 'weeks_after', interval: 'weeks' },
						{ key: 'months_before', interval: 'months' },
						{ key: 'months_after', interval: 'months' },
					],
					paymentScheduleTemplates: [paymentScheduleTemplate],
				},
			})
		);

		allocationPayments.setData({
			allocationCreatedAt: '2023-03-10',
			grantEndDate: '2023-03-10',
		});

		allocationPayments.vm.selectPaymentScheduleTemplate(null, paymentScheduleTemplate.slug);

		expect(allocationPayments.vm.form.allocationPayments[0].date_due).to.equal('2023-03-12');
		expect(allocationPayments.vm.form.allocationPayments[1].date_due).to.equal('2023-03-14');
		expect(allocationPayments.vm.form.allocationPayments[2].date_due).to.equal(
			moment().add(1, 'day').format('YYYY-MM-DD')
		);
	});

	it('calculates exceed amount', () => {
		const allocationPayments = shallowMount(AllocationPayments, defaultAllocationPayments());
		allocationPayments.setData({
			form: {
				allocationPayments: [
					makeAllocationPayment({
						id: 1,
						payment_method_id: null,
						status: 'scheduled',
						reference: '#123456',
						date_due: '2023-03-03',
						date_paid: null,
						amount: 306309.78,
					}),
					makeAllocationPayment({
						id: 1,
						payment_method_id: null,
						status: 'scheduled',
						reference: '#123457',
						date_due: '2023-03-03',
						date_paid: null,
						amount: 256208.78,
					}),
					makeAllocationPayment({
						id: 1,
						payment_method_id: null,
						status: 'scheduled',
						reference: '#123457',
						date_due: '2023-03-03',
						date_paid: null,
						amount: 134079.78,
					}),
				],
			},
			allocationId: 1,
			allocationSlug: 'ABCDEF',
			currency: 'EUR',
			allocatedAmount: 696598.34,
		});

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(3);
		expect(allocationPayments.vm.allocatedAmount).to.equal(696598.34);
		expect(allocationPayments.vm.paymentsScheduledAmount).to.equal(696598.34);
		expect(allocationPayments.vm.exceedsAllocationAmount).to.be.false;
	});

	it('failed permanently payments are not taking into account when calculating max amount', () => {
		const allocationPayments = shallowMount(AllocationPayments, defaultAllocationPayments());
		allocationPayments.setData({
			form: {
				allocationPayments: [
					makeAllocationPayment({
						id: 1,
						payment_method_id: null,
						status: 'scheduled',
						reference: '#123456',
						date_due: '2023-03-03',
						date_paid: null,
						amount: 200.0,
					}),
					makeAllocationPayment({
						id: 2,
						payment_method_id: null,
						status: 'failed_permanently',
						reference: '#123457',
						date_due: '2023-03-03',
						date_paid: null,
						amount: 100.0,
					}),
					makeAllocationPayment({
						id: 3,
						payment_method_id: null,
						status: 'scheduled',
						reference: '#123457',
						date_due: '2023-03-03',
						date_paid: null,
						amount: 300.0,
					}),
				],
			},
			allocationId: 1,
			allocationSlug: 'ABCDEF',
			currency: 'EUR',
			allocatedAmount: 1000.0,
		});

		expect(allocationPayments.vm.form.allocationPayments.length).to.equal(3);
		expect(allocationPayments.vm.allocatedAmount).to.equal(1000.0);
		expect(allocationPayments.vm.paymentsScheduledAmount).to.equal(500.0);
		expect(allocationPayments.vm.exceedsAllocationAmount).to.be.false;
	});

	it('should transform parameters before sent', async () => {
		const allocationPayments = shallowMount(
			AllocationPayments,
			defaultAllocationPayments({
				propsData: {
					paymentMethods: [{ id: 1, slug: 'bank123' }],
					routes,
				},
				mocks: {
					$http: {
						post: () => Promise.resolve({ data: {} }),
						put: () => Promise.resolve({ data: {} }),
					},
				},
			})
		);

		allocationPayments.vm.$store.dispatch('allocationPayments/openModalWithAllocation', allocation);

		const allocationPayment = makeAllocationPayment({
			id: 1,
			payment_method_id: 1,
			status: 'scheduled',
			reference: '#123456',
			date_due: '2025-07-16',
			date_paid: null,
			amount: 10,
			slug: 'ABCDEF',
		});

		const postSpy = sinon.spy(() => Promise.resolve({ data: {} }));
		const putSpy = sinon.spy(() => Promise.resolve({ data: {} }));
		allocationPayments.vm.$http.post = postSpy;
		allocationPayments.vm.$http.put = putSpy;

		allocationPayments.vm.getSaveType(allocationPayment);

		expect(putSpy.calledOnce).to.be.true;
		const sentParameters = putSpy.firstCall.args[1];

		expect(sentParameters).to.include({
			payment_method: 'bank123',
			allocation: 'ABCDEF',
		});
		expect(sentParameters.payment_method_id).to.be.undefined;
		expect(sentParameters.allocation_id).to.be.undefined;
	});

	it('should send empty string for payment method if not exists', async () => {
		const allocationPayments = shallowMount(
			AllocationPayments,
			defaultAllocationPayments({
				propsData: {
					paymentMethods: [],
					routes,
				},
				mocks: {
					$http: {
						post: () => Promise.resolve({ data: {} }),
						put: () => Promise.resolve({ data: {} }),
					},
				},
			})
		);

		allocationPayments.vm.$store.dispatch('allocationPayments/openModalWithAllocation', allocation);

		const allocationPayment = makeAllocationPayment({
			id: 1,
			payment_method_id: null,
			status: 'scheduled',
			reference: '#123456',
			date_due: '2025-07-16',
			date_paid: null,
			amount: 10,
			slug: 'ABCDEF',
		});

		const postSpy = sinon.spy(() => Promise.resolve({ data: {} }));
		const putSpy = sinon.spy(() => Promise.resolve({ data: {} }));
		allocationPayments.vm.$http.post = postSpy;
		allocationPayments.vm.$http.put = putSpy;

		allocationPayments.vm.getSaveType(allocationPayment);

		expect(putSpy.calledOnce).to.be.true;
		const sentParameters = putSpy.firstCall.args[1];

		expect(sentParameters).to.include({
			payment_method: '',
			allocation: 'ABCDEF',
		});
		expect(sentParameters.payment_method_id).to.be.undefined;
		expect(sentParameters.allocation_id).to.be.undefined;
	});
});
