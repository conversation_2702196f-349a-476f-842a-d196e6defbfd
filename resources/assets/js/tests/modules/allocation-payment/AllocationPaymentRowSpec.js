import actions from '@/lib/store/modules/allocation-payment/actions';
import AllocationPaymentRow from '@/modules/allocation-payment/components/AllocationPaymentRow.vue';
import { expect } from 'chai';
import { makeAllocationPayment } from '@/modules/allocation-payment/components/AllocationPaymentType';
import mutations from '@/lib/store/modules/allocation-payment/mutations';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

let store, defaultAllocationPayment;

const state = (options = {}) => ({
	allocation: { id: 1 },
	editOnlyAllocationPayment: defaultAllocationPayment,
	modalIsOpen: true,
	...options,
});

const localVue = createLocalVue();
localVue.use(Vuex);

const lang = { get: () => '' };

describe('AllocationPaymentRow', () => {
	beforeEach(() => {
		store = (options) =>
			new Vuex.Store({
				modules: {
					allocationPayments: {
						namespaced: true,
						state: state,
						actions: actions,
						mutations: mutations,
					},
				},
				...options,
			});

		defaultAllocationPayment = (options = {}) => {
			const defaultOptions = {
				provide: { lang },
				propsData: {
					index: 1,
					value: makeAllocationPayment({
						id: 1,
						slug: 'ABCDEF',
						amount: 123,
						allocation_id: 1,
						comments: [
							{
								id: 1,
							},
						],
					}),
				},
				methods: {},
				computed: {
					isLocked: () => false,
					canComment: () => true,
				},
				store: store(),
				localVue,
			};

			return { ...defaultOptions, ...options };
		};
	});

	const modeState = {
		isDeleting: () => false,
		isSaving: () => false,
		isFetching: () => false,
		isEmptyMode: () => false,
		isEditMode: () => false,
	};

	it('should display comments icon when modal is in empty mode', () => {
		const allocationPaymentRow = shallowMount(AllocationPaymentRow, defaultAllocationPayment());

		allocationPaymentRow.setData({
			modeState: {
				...modeState,
				isEmptyMode: () => true,
			},
		});
		expect(allocationPaymentRow.vm.commentIconCanBeDisplayed).to.be.true;
	});

	it('should display comments icon when modal is in edit mode', () => {
		const allocationPaymentRow = shallowMount(AllocationPaymentRow, defaultAllocationPayment());

		allocationPaymentRow.setData({
			modeState: {
				...modeState,
				isEditMode: () => true,
			},
		});
		expect(allocationPaymentRow.vm.commentIconCanBeDisplayed).to.be.true;
	});

	it('should hide comments icon when modal is in deleting mode', () => {
		const allocationPaymentRow = shallowMount(AllocationPaymentRow, defaultAllocationPayment());

		allocationPaymentRow.setData({
			modeState: {
				...modeState,
				isDeleting: () => true,
			},
		});
		expect(allocationPaymentRow.vm.commentIconCanBeDisplayed).to.be.false;
	});

	it('should hide comments icon when modal is in saving mode', () => {
		const allocationPaymentRow = shallowMount(AllocationPaymentRow, defaultAllocationPayment());

		allocationPaymentRow.setData({
			modeState: {
				...modeState,
				isSaving: () => true,
			},
		});
		expect(allocationPaymentRow.vm.commentIconCanBeDisplayed).to.be.false;
	});

	it('should hide comments icon when modal is in fetching mode', () => {
		const allocationPaymentRow = shallowMount(AllocationPaymentRow, defaultAllocationPayment());

		allocationPaymentRow.setData({
			modeState: {
				...modeState,
				isFetching: () => true,
			},
		});
		expect(allocationPaymentRow.vm.commentIconCanBeDisplayed).to.be.false;
	});
});
