import { useVideoPlayerApi } from '@/domain/services/VideoPlayer/VideoPlayerApi';
import { DataRequestMethod, DataResponse, dataSource } from '@/domain/services/Api/DataSource';
import { describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: () => ({}),
}));

describe('VideoPlayerApi', () => {
	it('should put log for given id and payload', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-file-id' } as DataResponse<unknown>);

		const response = await useVideoPlayerApi.putLog('fileId-321', { watched: 41 });

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/video-log/fileId-321/log',
			method: DataRequestMethod.PUT,
			data: { watched: 41 },
			headers: {},
		});
		expect(response).toBe('test-file-id');
	});
});
