// @vitest-environment jsdom
import { countCharacters, countWords } from './Counter';
import { describe, expect, it } from 'vitest';

describe('countWords', () => {
	it('returns correct word count for a simple sentence', () => {
		const content = 'Hello world';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	it('returns zero for an empty string', () => {
		const content = '';
		const result = countWords(content);
		expect(result).toBe(0);
	});

	it('counts words excluding html', () => {
		const content = '<p>Text <strong>in</strong><font style="color: red">paragraph</font></p><p>Foo <i>bar</i></p>';
		const result = countWords(content);
		expect(result).toBe(4);
	});

	it('counts words correctly with punctuation', () => {
		const content = 'Hello, world!';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	it('counts words correctly with multiple spaces', () => {
		const content = 'Hello   world';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	it('counts words correctly with non-breaking spaces', () => {
		const content = 'Hello&nbsp;new&nbsp;world';
		const result = countWords(content);
		expect(result).toBe(3);
	});

	it('counts words correctly with special characters', () => {
		const content = 'Hello @world!';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	// Checks fallback version
	it('counts words correctly with mixed languages', () => {
		const content = 'Hello 世界';
		const result = countWords(content);
		expect(result).toBe(3);
	});

	it('counts words correctly with hyphenated words', () => {
		const content = 'state-of-the-art';
		const result = countWords(content);
		expect(result).toBe(1);
	});

	it('counts words correctly with newline characters', () => {
		const content = 'Hello\nworld';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	it('counts words correctly with Chinese characters', () => {
		const content = '你好世界';
		const result = countWords(content);
		expect(result).toBe(4);
	});

	it('counts words correctly with Japanese characters', () => {
		const content = 'こんにちは世界';
		const result = countWords(content);
		expect(result).toBe(7);
	});

	it('counts words correctly with Thai characters', () => {
		const content = 'การตอบกลับอีเมลต้องเป็นที่อยู่อีเมลที่ถูกต้องหรือหมายเลขโทรศัพท์มือถือในรูปแบบสากล';
		const result = countWords(content);
		expect(result).toBeGreaterThanOrEqual(21);
	});

	it('counts words correctly with Korean characters', () => {
		const content = '안녕하세요 세계';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	it('counts urls as single word', () => {
		const urls = [
			'https://jedi.force.dev/entry-form/manager/xJZWVOpA/edit?tabSlug=rYVaPVrl',
			'jedi.force.dev/entry-form/manager/xJZWVOpA/edit?tabSlug=rYVaPVrl',
			'www.force.dev',
			'force.dev',
		];

		urls.forEach((content) => {
			const result = countWords(content);
			expect(result).toBe(1);
		});
	});

	it("counts words in figure's caption correctly", () => {
		const content =
			'<figure class="image"><img src="src" alt="alt"><figcaption>One Two</figcaption></figure><p>Three</p>';
		const result = countWords(content);
		expect(result).toBe(3);
	});

	it('counts words in list items correctly', () => {
		const content = '<ul><li>One</li><li>Two</li><li>Three</li></ul>';
		const result = countWords(content);
		expect(result).toBe(3);
	});

	it('handles word count with <br> tags properly', () => {
		const content = 'Hello<br>world';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	it('does not count `&amp;` as a word', () => {
		const content = 'Hello &amp; world';
		const result = countWords(content);
		expect(result).toBe(2);
	});

	it('does not count `&` as a word', () => {
		const content = 'Hello & world';
		const result = countWords(content);
		expect(result).toBe(2);
	});
});

describe('countCharacters', () => {
	it('counts characters excluding html', () => {
		expect(countCharacters('<p><strong style="">1</strong>2 4</p><p>5 <i>7</i></p>')).to.equal(7);
	});

	it('counts sequence of characters correctly', () => {
		expect(countCharacters('123')).to.equal(3);
	});

	it('counts sequence of characters with spaces correctly', () => {
		expect(countCharacters('1 2')).to.equal(3);
	});

	it('counts sequence of characters with newlines correctly', () => {
		expect(countCharacters('1\n2\n3')).to.equal(3);
		expect(countCharacters('<p>1</p><p>2</p><p>3</p>')).to.equal(3);
	});

	it('counts sequence of characters with iconograph characters correctly', () => {
		expect(countCharacters('こんにちは世界')).to.equal(7);
	});

	it('counts sequence of characters with Korean characters correctly', () => {
		expect(countCharacters('안녕하세요 세계')).to.equal(8);
	});
});
