/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable camelcase */
import { makeAllocationPayment } from '@/modules/allocation-payment/components/AllocationPaymentType';

export type Allocation = {
	id: number | null;
	slug: string | null;
	fundId: number | null;
	entryId: number | null;
	currency: string | null;
	name: string | null;
	formattedAmount: string | null;
	amount: number | null;
	tags: string[] | null;
	paid: string | null;
	created_at: string | null;
	grantEndDate: string | null;
	allocationPayments: unknown[];
	canBeCancelledAndDelete: () => boolean;
	hasDBRecord: () => boolean;
	hasAllocationPayments: () => boolean;
};

export const makeAllocation = (allocation: any = {}): Allocation => {
	const id = allocation.id ?? null;
	const slug = allocation.slug ?? null;
	const fundId = allocation.fundId ?? null;
	const entryId = allocation.entryId ?? null;
	const currency = allocation.currency ?? null;
	const name = allocation.name ?? null;
	const formattedAmount = allocation.amount ?? null;
	const amount = allocation.rawAmount ?? null;
	const tags = allocation.tags ?? null;
	const paid = allocation.paid ?? null;
	const created_at = allocation.created_at ?? null;
	const grantEndDate = allocation.grantEndDate ?? null;

	const allocationPayments = allocation.allocationPayments
		? allocation.allocationPayments.map((allocationPayment: any) => makeAllocationPayment(allocationPayment))
		: [];

	const canBeCancelledAndDelete = () => id === null || fundId === null;

	const hasDBRecord = () => id !== null;
	const hasAllocationPayments = () => allocationPayments.length > 0;

	return {
		id,
		slug,
		fundId,
		entryId,
		currency,
		name,
		formattedAmount,
		amount,
		tags,
		paid,
		created_at,
		grantEndDate,
		allocationPayments,
		canBeCancelledAndDelete,
		hasDBRecord,
		hasAllocationPayments,
	};
};
/* eslint-enable @typescript-eslint/naming-convention */
/* eslint-enable camelcase */
