<template>
	<fields-placeholder v-if="!loaded"></fields-placeholder>
	<div v-else ref="container" aria-live="polite" tabindex="0">
		<TabErrors :tab="selectedTab"></TabErrors>
		<span class="sr-only" tabindex="-1">{{ selectedTab.name }}</span>
		<div class="island island--content-block-wrap">
			<component
				:is="contentBlockComponent"
				:key="'tab-info-' + selectedTab.id"
				content-block-key="tab-info"
				:content-block="contentBlock"
				model-name="Tab"
				:model-id="selectedTab.id"
			/>
		</div>
		<alert
			v-if="selectedTab.readonly"
			type="warning"
			:message="lang.get('entries.form.tab_readonly')"
			icon-name="alert-warning"
		></alert>
		<keep-alive>
			<component
				:is="tabComponent"
				v-if="selectedTab"
				:key="'tab-' + '-' + selectedTab.type + '-' + selectedTab.slug"
				:tab="selectedTab"
				:transition="transition"
				:configure="configure"
			/>
		</keep-alive>
		<modal
			:id="'eligibility-' + selectedTab.id"
			v-model="eligibilityContentBlockVisible"
			:header="false"
			:body="eligibilityContentBlock"
			:header-close-button="false"
			:confirm-button-label="lang.get('buttons.confirm')"
			:close-button="false"
			@confirmed="resetEligibilityContentBlock"
		></modal>
	</div>
</template>

<script>
import { mapGetters, mapActions, mapState, mapMutations } from 'vuex';
import components from './index';
import FieldsPlaceholder from './../Fields/FieldsPlaceholder.vue';
import ContentBlock from '@/lib/components/ContentBlocks/ContentBlock';
import EditableContentBlock from '@/lib/components/ContentBlocks/EditableContentBlock';
import TabErrors from './TabErrors';
import { Modal } from 'vue-bootstrap';
import Alert from '@/lib/components/Shared/Alert';

export default {
	inject: ['lang'],
	components: {
		...components,
		FieldsPlaceholder,
		ContentBlock,
		EditableContentBlock,
		TabErrors,
		Modal,
		Alert,
	},
	props: {
		loaded: {
			type: Boolean,
			required: true,
		},
		transitions: {
			type: Boolean,
		},
		configure: {
			type: Boolean,
			required: true,
		},
	},
	watch: {
		selectedTab(oldTab, newTab) {
			oldTab = oldTab || {};
			newTab = newTab || {};

			if (oldTab.id !== newTab.id) {
				this.$nextTick(this.focus);
			}
		},
	},
	computed: {
		...mapGetters('entryForm', ['selectedTab']),
		...mapState('entryForm', ['hasManagerRole', 'contentBlocks', 'eligibilityContentBlock']),
		transition() {
			return this.transitions ? 'push' : '';
		},
		tabComponent() {
			return this.selectedTab.type + 'Tab';
		},
		contentBlockComponent() {
			return this.hasManagerRole ? 'EditableContentBlock' : 'ContentBlock';
		},
		contentBlock() {
			return this.contentBlocks.find((c) => c.id === this.selectedTab.contentblockId);
		},
		eligibilityContentBlockVisible() {
			return !!this.eligibilityContentBlock;
		},
	},
	created() {
		window.addEventListener('popstate', this.popstateListener);
	},
	beforeDestroy() {
		window.removeEventListener('popstate', this.popstateListener);
	},
	methods: {
		...mapActions('entryForm', ['selectTabBySlug']),
		...mapMutations('entryForm', ['setEligibilityContentBlock']),
		popstateListener(e) {
			this.selectTabBySlug(e.state.slug);
		},
		focus() {
			const container = this.$refs.container ? this.$refs.container : null;

			if (container) {
				container.querySelector('span.sr-only').focus();
			}
		},
		resetEligibilityContentBlock() {
			this.setEligibilityContentBlock(null);
		},
	},
};
</script>
