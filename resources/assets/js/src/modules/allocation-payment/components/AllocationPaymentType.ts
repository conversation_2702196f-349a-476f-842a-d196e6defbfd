/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable camelcase */

import * as E from 'fp-ts/Either';
import * as J from 'fp-ts/Json';
import { pipe } from 'fp-ts/function';
import { isArray, isNullOrUndefined, isString } from '@/domain/utils/TypePredicates';

const isEmptyString = (value: unknown): boolean => isString(value) && value.trim() === '';

type Comment = {
	comment: string;
	content: string;
	files: unknown[];
	id: number;
	internal: boolean;
	isApi: boolean;
	relativeTime: string;
	slug: string;
	user: string;
	userId: number;
};

type AllocationPayment = {
	id: number | null;
	payment_method_id: number | null;
	status: string | null;
	originalStatus: string | null;
	reference: string | null;
	date_due: string | null;
	date_paid: string | null;
	amount: number | null;
	slug: string | null;
	allocation_id: number | null;
	currency: string | null;
	comments: Comment[];
	hasComments: () => boolean;
	getRequestParameters: (parameters: unknown[]) => Record<string, unknown>;
	getData: () => Record<string, unknown>;
	canBeCancelledAndDelete: () => boolean;
	hasDBRecord: () => boolean;
	canBeSavedToDatabase: () => boolean;
	getInstance: () => AllocationPayment;
	setComments: (newComments: unknown[]) => AllocationPayment;
};

const parseNullableInt = (val: unknown): number | null => {
	if (isNullOrUndefined(val) || isEmptyString(val)) return null;
	const num = Number(val);
	return isNaN(num) ? null : parseInt(String(num), 10);
};

const parseNullableFloat = (val: unknown): number | null => {
	if (isNullOrUndefined(val) || isEmptyString(val)) return null;
	const num = Number(val);
	return isNaN(num) ? null : parseFloat(String(num));
};

const makeAllocationPayment = (allocationPayment: any = {}): AllocationPayment => {
	const id = parseNullableInt(allocationPayment?.id);
	const payment_method_id = parseNullableInt(allocationPayment?.payment_method_id ?? allocationPayment?.paymentMethodId);

	const originalStatus = allocationPayment?.status ?? null;
	const status = allocationPayment?.status ?? null;
	const reference = allocationPayment?.reference ?? null;
	const date_due = allocationPayment?.date_due ?? allocationPayment?.dateDue ?? null;
	const date_paid = allocationPayment?.date_paid ?? allocationPayment?.datePaid ?? null;
	const amount = parseNullableFloat(allocationPayment?.amount);
	const slug = allocationPayment?.slug ?? null;
	const allocation_id = parseNullableInt(allocationPayment?.allocation_id ?? allocationPayment?.allocationId);
	const currency = allocationPayment?.currency ?? null;

	const rawComments = getJson(allocationPayment?.comments);
	const comments: Comment[] = isArray(rawComments) ? (rawComments as Comment[]) : [];

	const hasComments = () => comments.length > 0;
	const getRequestParameters = (parameters: unknown[]): Record<string, unknown> => ({
		...parameters,
		id,
		status,
		reference,
		date_due,
		date_paid,
		amount,
		slug,
		currency,
	});

	const getData = (): Record<string, unknown> => ({
		id: id,
		payment_method_id: payment_method_id,
		status: status,
		reference: reference,
		date_due: date_due,
		date_paid: date_paid,
		amount: amount,
	});

	const canBeCancelledAndDelete = () =>
		id === null &&
		payment_method_id === null &&
		status === null &&
		reference === null &&
		date_due === null &&
		date_paid === null &&
		amount === null;

	const hasDBRecord = () => id !== null;

	const canBeSavedToDatabase = () => !hasDBRecord() && status !== null && amount !== null;

	const getInstance = () =>
		makeAllocationPayment({
			id,
			payment_method_id,
			status,
			reference,
			date_due,
			date_paid,
			amount,
			slug,
			allocation_id,
			currency,
			comments,
		});

	const setComments = (newComments: unknown[]) =>
		makeAllocationPayment(Object.assign({}, allocationPayment, { comments: newComments }));

	function getJson(jsonValue: any) {
		return pipe(
			J.parse(jsonValue),
			E.getOrElse(() => jsonValue)
		);
	}

	return {
		id,
		payment_method_id,
		status,
		originalStatus,
		reference,
		date_due,
		date_paid,
		amount,
		slug,
		allocation_id,
		currency,
		comments,
		hasComments,
		getRequestParameters,
		getData,
		canBeCancelledAndDelete,
		hasDBRecord,
		canBeSavedToDatabase,
		getInstance,
		setComments,
	} as const;
};

export { makeAllocationPayment, type AllocationPayment };
/* eslint-enable @typescript-eslint/naming-convention */
/* eslint-enable camelcase */
