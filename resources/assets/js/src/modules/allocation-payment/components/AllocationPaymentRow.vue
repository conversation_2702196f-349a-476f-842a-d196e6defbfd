<template>
	<!-- eslint-disable vue/no-mutating-props -->
	<tr class="allocation-payment-row">
		<td class="overflow-cell">
			<dropdown
				v-if="canDisplayDropDown"
				ref="dropdown-menu"
				button-class="dropdown-toggle"
				container-class="action-overflow"
				menu-class="dropdown-menu-left"
			>
				<span slot="label">
					<i class="af-icons af-icons-action-overflow">
						<span class="sr-only">Menu for </span>
					</i>
				</span>
				<li v-if="canBeEdited">
					<button class="dropdown-menu-item focus-outline" @click.stop.prevent="editAllocationPayment">
						{{ lang.get('buttons.edit') }}
					</button>
				</li>
				<li v-if="canBeDeleted">
					<button class="dropdown-menu-item focus-outline" @click.stop.prevent="deleteAllocationPayment">
						{{ lang.get('buttons.delete') }}
					</button>
				</li>
				<li v-if="canComment">
					<button class="dropdown-menu-item focus-outline" @click.stop.prevent="commentAllocationPayment">
						{{ lang.get('allocation-payments.buttons.comment') }}
					</button>
				</li>
			</dropdown>
		</td>
		<td>
			<div v-if="isEditMode" :class="['form-group', { error: hasError('payment_method_id') }]">
				<select-field
					v-if="isEditMode"
					:aria-label="lang.get('allocation-payments.form.table.headers.method')"
					:items="getPaymentMethods"
					value-property="name"
					:empty-option="true"
					:value="value.payment_method_id"
					:aria-invalid="hasError('payment_method_id')"
					:aria-describedby="getAriaDescribed('payment_method_id')"
					:disabled="fieldIsDisabled('payment_method_id')"
					@selected="(name, selected) => update('payment_method_id', selected)"
				/>
				<div
					v-if="hasError('payment_method_id')"
					:id="getAriaDescribed('payment_method_id')"
					class="alert-error sticky inline"
					role="alert"
					v-text="getError('payment_method_id')"
				></div>
			</div>
			<span v-if="!isEditMode" :class="{ deleted: isPaymentMethodDeleted }" v-text="paymentMethodName"></span>
		</td>
		<td>
			<a v-if="commentIconCanBeDisplayed" @click.stop.prevent="commentAllocationPayment">
				<i class="af-icons af-icons-comments"></i>
			</a>
		</td>
		<td>
			<div v-if="isEditMode" :class="['form-group', { error: hasError('reference') }]">
				<input
					v-if="isEditMode"
					:value="value.reference"
					:aria-label="lang.get('allocation-payments.form.table.headers.reference')"
					type="text"
					class="form-control"
					maxlength="32"
					:aria-invalid="hasError('reference')"
					:aria-describedby="getAriaDescribed('reference')"
					:disabled="fieldIsDisabled('reference')"
					@input="update('reference', $event.target.value)"
				/>
				<div
					v-if="hasError('reference')"
					:id="getAriaDescribed('reference')"
					class="alert-error sticky inline"
					role="alert"
					v-text="getError('reference')"
				></div>
			</div>
			<span v-if="!isEditMode" v-text="referenceName"></span>
		</td>
		<td>
			<div v-if="isEditMode" :class="['form-group', { error: hasError('date_due') }]">
				<datepicker
					v-if="isEditMode"
					:id="`date-due-${index}`"
					:placeholder="lang.get('allocation-payments.form.table.headers.date_due')"
					:value="value.date_due"
					mode="date"
					class="date-datepicker"
					:highlight-today="false"
					:timezones="timezones"
					:moment-locale="momentLocale"
					:moment-date-format="momentDateFormat"
					:moment-time-format="momentTimeFormat"
					:aria-invalid="hasError('date_due')"
					:aria-describedby="getAriaDescribed('date_due')"
					:disabled="fieldIsDisabled('date_due')"
					@changed="(date) => update('date_due', date)"
				/>
				<div
					v-if="hasError('date_due')"
					:id="getAriaDescribed('date_due')"
					class="alert-error sticky inline"
					role="alert"
					v-text="getError('date_due')"
				></div>
			</div>
			<span v-if="!isEditMode" v-text="dateDueName"></span>
		</td>
		<td>
			<div v-if="isEditMode" :class="['form-group', { error: hasError('date_paid') }]">
				<datepicker
					v-if="isEditMode"
					:id="`date-paid-${index}`"
					:placeholder="lang.get('allocation-payments.form.table.headers.date_paid')"
					:value="value.date_paid"
					mode="date"
					class="date-datepicker"
					:highlight-today="false"
					:timezones="timezones"
					:moment-locale="momentLocale"
					:moment-date-format="momentDateFormat"
					:moment-time-format="momentTimeFormat"
					:aria-invalid="hasError('date_paid')"
					:aria-describedby="getAriaDescribed('date_paid')"
					:disabled="fieldIsDisabled('date_paid')"
					@changed="(date) => update('date_paid', date)"
				/>
				<div
					v-if="hasError('date_paid')"
					:id="getAriaDescribed('date_paid')"
					class="alert-error sticky inline"
					role="alert"
					v-text="getError('date_paid')"
				></div>
			</div>
			<span v-if="!isEditMode" v-text="datePaidName"></span>
		</td>
		<td class="text-right">
			<div v-if="isEditMode" :class="['form-group', { error: hasError('status') }]">
				<select-field
					v-if="isEditMode"
					:aria-label="lang.get('allocation-payments.form.table.headers.status')"
					:items="statuses"
					id-property="key"
					value-property="name"
					:empty-option="true"
					:value="value.status"
					aria-required="true"
					:aria-invalid="hasError('status')"
					:aria-describedby="getAriaDescribed('status')"
					@selected="(name, selected) => update('status', selected)"
				/>
				<div
					v-if="hasError('status')"
					:id="getAriaDescribed('status')"
					class="alert-error sticky inline"
					role="alert"
					v-text="getError('status')"
				></div>
			</div>
			<span v-if="!isEditMode" v-text="statusName"></span>
		</td>
		<td class="text-right">
			<div v-if="isEditMode" :class="['form-group', { error: hasError('amount') }]">
				<currency-input
					ref="amount"
					:value="value.amount"
					:aria-label="lang.get('allocation-payments.form.table.headers.amount')"
					class="text-right form-control"
					:distraction-free="distractionFree"
					:currency="null"
					:locale="locale"
					:precision="2"
					:allow-negative="false"
					:value-range="valueRange"
					aria-required="true"
					:aria-invalid="hasError('amount')"
					:aria-describedby="getAriaDescribed('amount')"
					:disabled="fieldIsDisabled('amount')"
					@input="update('amount', $event)"
				/>
				<div
					v-if="hasError('amount')"
					:id="getAriaDescribed('amount')"
					class="alert-error sticky inline"
					role="alert"
					v-text="getError('amount')"
				></div>
			</div>
			<span v-if="!isEditMode" v-text="amountName"></span>
		</td>
	</tr>
</template>

<script lang="ts">
import { Datepicker, Dropdown, SelectField } from 'vue-bootstrap';
import {
	makeAllocationPayment,
	type AllocationPayment,
} from '@/modules/allocation-payment/components/AllocationPaymentType';
import modeState, { states as modeStates } from './states/mode.state';
import { CurrencyInput } from 'vue-currency-input';
import { formatMoney } from '@/lib/Money';
import { mapState } from 'vuex';
import moment from 'moment-timezone';
import { PropType } from 'vue';

export default {
	inject: ['lang'],

	name: 'AllocationPaymentRow',

	components: {
		Datepicker,
		Dropdown,
		SelectField,
		CurrencyInput,
	},

	props: {
		value: {
			type: Object as PropType<AllocationPayment>,
			default: makeAllocationPayment,
		},
		index: {
			type: Number,
			required: true,
		},
		validationErrors: {
			type: Object,
			default: () => ({}),
		},
		mode: {
			type: String,
			default: 'pending',
		},
		paymentMethods: {
			type: Array,
			default: () => [],
		},
		statuses: {
			type: Array,
			default: () => [],
		},
		currency: {
			type: String,
			required: true,
		},
		locale: {
			type: String,
			required: true,
		},
		maxAmount: {
			type: Number,
			default: () => 0,
		},
		permissions: {
			type: Object,
			default: () => ({}),
		},
	},

	data() {
		return {
			modeState,
		};
	},

	computed: {
		...mapState('global', ['momentLocale', 'momentDateFormat', 'momentTimeFormat', 'timezones']),

		...mapState('allocationPayments', ['editOnlyAllocationPayment']),

		valueRange() {
			return {
				min: 0,
				max: this.maxAmount,
			};
		},

		distractionFree() {
			return {
				hideCurrencySymbol: true,
				hideGroupingSymbol: false,
				hideNegligibleDecimalDigits: false,
			};
		},

		isLocked() {
			if (this.editOnlyAllocationPayment === null) {
				return false;
			}

			return parseInt(this.editOnlyAllocationPayment.id) !== parseInt(this.value.id);
		},

		isEditMode() {
			return (
				this.modeState.currentState === modeStates.edit && this.modeState.currentAllocationPaymentIndex === this.index
			);
		},

		paymentMethodName() {
			return this.paymentMethod?.name || '—';
		},

		paymentMethod() {
			if (!this.value?.payment_method_id) {
				return null;
			}

			return this.paymentMethods.filter((paymentMethod) => paymentMethod.id === this.value?.payment_method_id)?.[0];
		},

		isPaymentMethodDeleted() {
			return this.paymentMethod?.deletedAt !== null;
		},

		statusName() {
			if (!this.value?.status) {
				return '—';
			}

			return this.statuses.filter((status) => status.key === this.value?.status)?.[0]?.name;
		},

		referenceName() {
			return this.value?.reference || '—';
		},

		dateDueName() {
			if (!this.value?.date_due) {
				return '—';
			}

			return moment(this.value.date_due).format(this.momentDateFormat);
		},

		datePaidName() {
			if (!this.value?.date_paid) {
				return '—';
			}

			return moment(this.value.date_paid).format(this.momentDateFormat);
		},

		amountName() {
			if (!this.value?.amount) {
				return '—';
			}

			return formatMoney(this.value?.amount, this.locale, this.currency);
		},

		canBeEdited() {
			if (!this.permissions.hasPaymentPermissions) {
				return false;
			}

			return this.modeState.currentAllocationPaymentIndex !== this.index;
		},

		canBeDeleted() {
			if (!this.permissions.hasPaymentPermissions) {
				return false;
			}

			if (this.editOnlyAllocationPayment !== null) {
				return false;
			}

			if (this.value.hasDBRecord() && this.value.status !== 'scheduled') {
				return false;
			}

			return true;
		},

		canComment() {
			return this.value.hasDBRecord();
		},

		canDisplayDropDown() {
			if (this.isLocked) {
				return false;
			}

			return this.canBeEdited || this.canBeDeleted || this.canComment;
		},

		commentIconCanBeDisplayed() {
			if (this.modeState.isDeleting() || this.modeState.isSaving() || this.modeState.isFetching() || !this.canComment) {
				return false;
			}

			return this.value.hasComments();
		},

		getPaymentMethods() {
			return this.paymentMethods.filter((paymentMethod) => paymentMethod.deletedAt === null);
		},
	},

	methods: {
		editAllocationPayment() {
			this.modeState.send(this.modeState.Transitions.EDIT, this.index);
			this.hideDropdown();
			this.$emit('reset-errors');
			this.$emit('backup');
		},

		deleteAllocationPayment() {
			this.hideDropdown();
			this.$emit('delete');
		},

		commentAllocationPayment() {
			this.hideDropdown();
			this.$emit('comment');
		},

		hideDropdown() {
			if (this.$refs['dropdown-menu']) {
				this.$refs['dropdown-menu'].isOpen = false;
			}
		},

		update(key, value) {
			const updated = makeAllocationPayment({ ...this.value, [key]: value });
			this.$emit('input', updated);
		},

		fieldIsDisabled(field) {
			switch (field) {
				case 'payment_method_id':
				case 'reference':
				case 'date_due':
				case 'amount':
					if (this.isEditMode) {
						if (this.value.hasDBRecord()) {
							if (['processing', 'paid', 'failed', 'failed_permanently'].includes(this.value.originalStatus)) {
								return true;
							}
						}
					}

					return false;
				case 'date_paid':
					return (
						this.isEditMode &&
						this.value.hasDBRecord() &&
						['failed', 'failed_permanently'].includes(this.value.originalStatus)
					);
				case 'status':
					return false;
			}
		},

		canDisplayErrors() {
			return this.isEditMode;
		},

		hasError(field) {
			return this.canDisplayErrors && Object.prototype.hasOwnProperty.call(this.validationErrors || {}, field);
		},

		getError(field) {
			return this.hasError(field) && this.validationErrors?.[field]?.[0];
		},

		getAriaDescribed(field) {
			return this.hasError(field) ? `${field}${this.index}Error` : null;
		},
	},
};
</script>

<style scoped lang="scss">
td {
	input.form-control,
	select.form-control {
		&:disabled {
			background-color: #f2f2f2 !important;
		}

		&:not(:disabled) {
			background-color: white !important;
		}
	}
}

table {
	tbody {
		tr {
			td {
				vertical-align: top;
			}
		}
	}
}

.form-group {
	margin-bottom: 0;
}

.alert-error {
	text-align: left;
}
</style>
