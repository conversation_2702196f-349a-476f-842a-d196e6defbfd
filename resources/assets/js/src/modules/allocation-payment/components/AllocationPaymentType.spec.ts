import { makeAllocationPayment } from '@/modules/allocation-payment/components/AllocationPaymentType';
import { afterEach, beforeEach, describe, expect, it } from 'vitest';

let allocationPayment: ReturnType<typeof makeAllocationPayment>;

const validData = {
	id: '1',
	payment_method_id: '2',
	status: 'scheduled',
	reference: 'ref',
	date_due: '2023-01-01',
	date_paid: '2023-01-02',
	amount: '100.5',
	slug: 'slug',
	allocation_id: '3',
	currency: 'EUR',
	comments: [{ id: 1, text: 'foo' }],
};

describe('AllocationPaymentType', () => {
	beforeEach(() => {
		allocationPayment = makeAllocationPayment(validData);
	});
	afterEach(() => {
		allocationPayment = undefined as any;
	});

	it('should create an immutable object with all expected properties', () => {
		expect(allocationPayment.id).toBe(1);
		expect(allocationPayment.payment_method_id).toBe(2);
		expect(allocationPayment.status).toBe('scheduled');
		expect(allocationPayment.reference).toBe('ref');
		expect(allocationPayment.date_due).toBe('2023-01-01');
		expect(allocationPayment.date_paid).toBe('2023-01-02');
		expect(allocationPayment.amount).toBe(100.5);
		expect(allocationPayment.slug).toBe('slug');
		expect(allocationPayment.allocation_id).toBe(3);
		expect(allocationPayment.currency).toBe('EUR');
		expect(allocationPayment.comments).toEqual([{ id: 1, text: 'foo' }]);
		expect(allocationPayment).toBeDefined();
	});

	it('should parse numbers and nulls correctly for all numeric fields', () => {
		const obj = makeAllocationPayment({
			id: '',
			payment_method_id: undefined,
			amount: 'notanumber',
			allocation_id: null,
		});
		expect(obj.id).toBe(null);
		expect(obj.payment_method_id).toBe(null);
		expect(obj.amount).toBe(null);
		expect(obj.allocation_id).toBe(null);
	});

	it('should parse comments if given as a JSON string', () => {
		const arr = [{ id: 1 }];
		const obj = makeAllocationPayment({ comments: JSON.stringify(arr) });
		expect(obj.comments).toEqual(arr);
	});

	it('should use comments as-is if given as an array', () => {
		const arr = [{ id: 2 }];
		const obj = makeAllocationPayment({ comments: arr });
		expect(obj.comments).toEqual(arr);
	});

	it('should detect if there are any comments', () => {
		const obj = makeAllocationPayment({ comments: [{ id: 1 }] });
		expect(obj.hasComments()).toBe(true);
		const obj2 = makeAllocationPayment({ comments: [] });
		expect(obj2.hasComments()).toBe(false);
	});

	it('should merge request parameters and include all main properties', () => {
		const params = allocationPayment.getRequestParameters({ extra: 123 });
		expect(params.id).toBe(1);
		expect(params.amount).toBe(100.5);
		expect(params.extra).toBe(123);
	});

	it('should return only the main data fields with getData', () => {
		const obj = makeAllocationPayment({
			id: 1,
			amount: 10,
			status: 'scheduled',
			reference: 'r',
			date_due: 'd1',
			date_paid: 'd2',
			payment_method_id: 2,
		});
		expect(obj.getData()).toEqual({
			id: 1,
			payment_method_id: 2,
			status: 'scheduled',
			reference: 'r',
			date_due: 'd1',
			date_paid: 'd2',
			amount: 10,
		});
	});

	it('should consider as cancellable only if all main fields are null', () => {
		const obj = makeAllocationPayment({});
		expect(obj.canBeCancelledAndDelete()).toBe(true);
		const obj2 = makeAllocationPayment({ id: 1 });
		expect(obj2.canBeCancelledAndDelete()).toBe(false);
	});

	it('should check if there is a record (id is not null)', () => {
		const obj = makeAllocationPayment({ id: 1 });
		expect(obj.hasDBRecord()).toBe(true);
		const obj2 = makeAllocationPayment({});
		expect(obj2.hasDBRecord()).toBe(false);
	});

	it('should check if it can be saved to database (no id, but has status and amount)', () => {
		const obj = makeAllocationPayment({ status: 'scheduled', amount: 10 });
		expect(obj.canBeSavedToDatabase()).toBe(true);
		const obj2 = makeAllocationPayment({ id: 1, status: 'scheduled', amount: 10 });
		expect(obj2.canBeSavedToDatabase()).toBe(false);
		const obj3 = makeAllocationPayment({});
		expect(obj3.canBeSavedToDatabase()).toBe(false);
	});

	it('should create a new immutable instance with the same data', () => {
		const inst = allocationPayment.getInstance();
		expect(inst).not.toBe(allocationPayment);
		expect(inst.id).toBe(1);
		expect(inst.amount).toBe(100.5);
	});

	it('should create a new immutable object with updated comments', () => {
		const updated = allocationPayment.setComments([{ id: 2 }]);
		expect(updated.comments).toEqual([{ id: 2 }]);
		expect(updated.id).toBe(1);
	});

	it('should allow destructuring of properties and methods', () => {
		const { id, status, amount, getData } = allocationPayment;
		expect(id).toBe(1);
		expect(status).toBe('scheduled');
		expect(amount).toBe(100.5);
		expect(typeof getData).toBe('function');
	});

	it('should return sensible defaults for empty input', () => {
		const obj = makeAllocationPayment();
		expect(obj.id).toBe(null);
		expect(obj.amount).toBe(null);
		expect(obj.status).toBe(null);
		expect(obj.comments).toEqual([]);
	});

	it('should create a new row with all default values when no data is provided', () => {
		const obj = makeAllocationPayment();
		expect(obj.id).toBe(null);
		expect(obj.payment_method_id).toBe(null);
		expect(obj.status).toBe(null);
		expect(obj.reference).toBe(null);
		expect(obj.date_due).toBe(null);
		expect(obj.date_paid).toBe(null);
		expect(obj.amount).toBe(null);
		expect(obj.slug).toBe(null);
		expect(obj.allocation_id).toBe(null);
		expect(obj.currency).toBe(null);
		expect(obj.comments).toEqual([]);
	});

	it('should handle adding a new row with comments as a string', () => {
		const obj = makeAllocationPayment({ comments: '[{"id":1,"text":"test"}]' });
		expect(Array.isArray(obj.comments)).toBe(true);
		expect(obj.comments[0].id).toBe(1);
		expect(obj.comments[0].text).toBe('test');
	});
});
