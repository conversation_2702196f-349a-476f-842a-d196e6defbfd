import { Editor } from '@/lib/components/Shared/editor/TextEditor.types';
import { EditorUploadProps } from '@/lib/components/Shared/editor/uploads/Uploads.types';
import { initCKEditor } from '@/lib/components/Shared/editor/ckeditor/cke-config';
import { instantiateUITranslation } from '@/lib/components/Shared/editor/ckeditor/toolbar/translations';
import { isEqual } from 'lodash';
import { setActiveEditor } from '@/lib/components/Shared/MergeFields/services/activeEditor';
import { useContainer } from '@/domain/services/Container';
import { v4 as uuid } from 'uuid';
import { VueHooks } from '@/domain/services/VueHooks';
import { canAttachFiles, canUploadInlineImages } from '@/lib/components/Shared/editor/uploads/uploadsLogic';
import { EditorEmitters, EditorEvents } from '@/lib/components/Shared/editor/EditorEvents';
import { ref, Ref, SetupFunction } from 'vue';
import type { ToolbarConfig, ToolbarConfigItem } from 'ckeditor5';

type Props = EditorUploadProps & {
	defaultLanguage: string;
	disabled: boolean;
	fieldName: string;
	id: string;
	inActiveTab: boolean;
	inputClass: string;
	language: string;
	markdownGuideLabel: string;
	markdownGuideUrl: string;
	name: string | null;
	placeholder: string | null;
	spellChecker: boolean;
	toolbar: ToolbarConfigItem[];
	removeItemsToolbar: string[];
	translations: Record<string, string>;
	uploads: boolean;
	useHiddenInput: boolean;
	value: string;
	maximumCharacters?: number;
	maximumWords?: number;
};

type View = {
	currentHtml: Ref<string>;
	editorElementRef: Ref<HTMLElement | string>;
	editorToolbarRef: Ref<HTMLDivElement | null>;
	editorUniqueId: Ref<string>;
	isEditorFocused: Ref<boolean>;
	textarea: Ref<HTMLTextAreaElement | null>;
};

type Ctx = {
	emit: EditorEmitters;
};

const ckEditorController: SetupFunction<Props, View> = (props: Props, { emit }: Ctx): View => {
	const { onMounted, onBeforeUnmount, onWatch } = useContainer<VueHooks>();
	const currentHtml = ref(props.value);
	const isEditorFocused = ref(false);
	const editorUniqueId = ref('ckeditor-' + uuid());
	const editor: Ref<Editor | null> = ref(null);
	const textarea: Ref<HTMLTextAreaElement | null> = ref(null);
	const editorElementRef: Ref<HTMLElement | string> = ref('');
	const editorToolbarRef: Ref<HTMLDivElement | null> = ref(null);

	const getContent = () => {
		if (!editor.value) return '';

		return editor.value.getData();
	};

	const setContent = (html: null | string) => {
		if (editor.value) editor.value.setData(html ?? '');
	};

	const ckeditorInitialized = (newEditor: Editor) => {
		editor.value = newEditor;
		setContent(currentHtml.value);

		if (props.disabled) editor.value.enableReadOnlyMode(editorUniqueId.value);

		editor.value.model.document.on('change:data', onInput);
		editor.value.ui.focusTracker.on('change:isFocused', (evt, name, isFocused) => onFocus(isFocused));

		/**
		 * Addresses the CKEditor 5 dialog scrolling issue.
		 *
		 * When a dialog is opened, the page may scroll to the top unexpectedly.
		 * This workaround adjusts the overflow styles of the <body> and, if present,
		 * the '.app.has-tray' container to prevent this behaviour.
		 *
		 */
		const dialogPlugin = editor.value.plugins.get('Dialog');

		dialogPlugin.on('show', () => {
			const appTray = document.querySelector('.app.has-tray') as HTMLElement | null;
			const body = document.body;

			body.style.overflowY = 'visible';
			if (appTray) appTray.style.overflowY = 'visible';

			// Restore the original overflowY styles
			dialogPlugin.once('hide', () => {
				if (appTray) appTray.style.overflowY = 'auto';
				body.style.overflowY = 'scroll';
			});
		});

		if (editor.value.plugins.has('AttachFile')) {
			const attachFile = editor.value.plugins.get('AttachFile');
			if (attachFile) attachFile.on('upload', () => emit(EditorEvents.Upload));
		}

		// Attach the toolbar to the editor
		if (editorToolbarRef.value && editor.value.ui.view.toolbar.element) {
			editorToolbarRef.value.appendChild(editor.value.ui.view.toolbar.element);
		}
	};

	onWatch(
		() => props.value,
		(newValue) => {
			if (newValue !== getContent()) {
				setContent(newValue || '');
			}
		}
	);

	onWatch(
		() => props.disabled,
		(newValue) => {
			if (editor.value) {
				if (newValue) {
					editor.value.enableReadOnlyMode(editorUniqueId.value);
				} else {
					editor.value.disableReadOnlyMode(editorUniqueId.value);
				}
			}
		}
	);

	// Assert that the toolbar element changes when switching between field types
	onWatch(
		() => [props.toolbar, props.removeItemsToolbar],
		(newValue, oldValue) => {
			if (editor.value && !isEqual(oldValue, newValue)) {
				editor.value.destroy();

				if (editorElementRef.value && editorElementRef.value instanceof HTMLElement) {
					editorElementRef.value.innerHTML = '';
				}

				if (editorToolbarRef.value) {
					editorToolbarRef.value.innerHTML = '';
				}

				initEditor();
			}
		}
	);

	const onFocus = (isFocused: boolean) => {
		isEditorFocused.value = isFocused;

		if (isFocused) {
			setActiveEditor(editor.value);
			if (editor.value) {
				emit(EditorEvents.Focus, editor.value);
			}
		} else {
			onBlur();
		}
	};

	const onInput = () => {
		currentHtml.value = getContent();

		if (currentHtml.value !== getContent()) {
			setContent(currentHtml.value || '');
		}

		if (textarea.value) {
			// emit native keyup event
			textarea.value.dispatchEvent(new Event('input', { bubbles: true }));
			textarea.value.dispatchEvent(new Event('keyup', { bubbles: true }));
			textarea.value.dispatchEvent(new Event('change', { bubbles: true }));

			emit(EditorEvents.Input, currentHtml.value);
		}
	};

	const onBlur = () => {
		// emit native blur event
		const event = new Event('blur');

		if (textarea.value) {
			textarea.value.dispatchEvent(event);
			emit(EditorEvents.Blur);
		}
	};

	const editorToolbar = ({
		items,
		removeItems,
	}: {
		items: ToolbarConfigItem[];
		removeItems: string[];
	}): ToolbarConfig => {
		if (!canAttachFiles(props) && items.includes('attachFile')) {
			removeItems = [...removeItems, 'attachFile'];
		}

		if (!canUploadInlineImages(props) && items.includes('insertImage')) {
			removeItems = [...removeItems, 'insertImage'];
		}

		return {
			items,
			removeItems,
			shouldNotGroupWhenFull: true,
		};
	};

	const initEditor = async () => {
		await initCKEditor({
			element: editorElementRef.value,
			toolbar: editorToolbar({ items: props.toolbar, removeItems: props.removeItemsToolbar }),
			callback: ckeditorInitialized,
		});
	};

	onMounted(() => {
		instantiateUITranslation();
		initEditor();
	});

	onBeforeUnmount(() => {
		if (editor.value) editor.value.destroy();
	});

	return {
		currentHtml,
		editorElementRef,
		editorToolbarRef,
		editorUniqueId,
		isEditorFocused,
		textarea,
	};
};

export { Props, View, Ctx, ckEditorController };
