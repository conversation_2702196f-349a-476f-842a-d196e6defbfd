import { initCKEditor } from '@/lib/components/Shared/editor/ckeditor/cke-config';
import { SetupContext } from 'vue';
import { useContainer } from '@/domain/services/Container';
import { afterEach, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { ckEditorController, Props, View } from '@/lib/components/Shared/editor/ckeditor/CKEditor.controller';

vi.mock('@/domain/services/Container', () => ({ useContainer: vi.fn() }));
vi.mock('@/lib/components/Shared/editor/ckeditor/cke-config', () => ({ initCKEditor: vi.fn() }));
vi.mock('@ckeditor/ckeditor5-editor-classic/src/classiceditor', () => ({}));
vi.mock('@/lib/components/Shared/editor/ckeditor/toolbar/translations', () => ({ instantiateUITranslation: vi.fn() }));

vi.mock('tectoastr', () => ({
	error: vi.fn(),
}));

const ctx = { emit: vi.fn() } as unknown as SetupContext;

const defaultProps: Props = {
	value: '',
	name: null,
	fieldName: '',
	spellChecker: false,
	toolbar: ['bold'],
	removeItemsToolbar: ['italic'],
	markdownGuideLabel: '',
	markdownGuideUrl: '',
	placeholder: null,
	disabled: false,
	id: '123',
	inActiveTab: false,
	useHiddenInput: false,
	inputClass: '',
	defaultLanguage: 'en_GB',
	language: 'en_GB',
	allowsAttachments: false,
	allowsInlineImages: false,
	translations: {},
	uploads: false,
	uploaderObject: {},
	uploaderOptions: {},
};

describe('Editor controller', () => {
	const onPluginMock = vi.fn();
	const editorMock = {
		destroy: vi.fn(),
		setData: vi.fn(),
		model: { document: { on: vi.fn() } },
		ui: {
			focusTracker: { on: vi.fn() },
			view: { toolbar: { element: 'toolbar-element' } },
		},
		plugins: {
			get: () => ({ on: onPluginMock }),
			has: () => vi.fn().mockReturnValue(true),
		},
		enableReadOnlyMode: vi.fn(),
	};

	const onWatch = vi.fn();
	const onMounted = vi.fn();
	const onBeforeUnmount = vi.fn();
	let onMountedHook = () => vi.fn();
	let onBeforeUnmountHook = () => vi.fn();
	// eslint-disable-next-line no-unused-vars
	let watchCallback = (arg1: string[], arg2: string[]) => vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();
		vi.clearAllMocks();
		(useContainer as Mock).mockReturnValue({ onMounted, onBeforeUnmount, onWatch });
		(initCKEditor as Mock).mockImplementation(({ callback }) => callback(editorMock));
		(onMounted as Mock).mockImplementation((callback) => (onMountedHook = callback));
		(onBeforeUnmount as Mock).mockImplementation((callback) => (onBeforeUnmountHook = callback));
		(onWatch as Mock).mockImplementation((source, cb) => (watchCallback = cb));
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('should initEditor on mounted', () => {
		const view = ckEditorController(defaultProps, ctx) as View;

		view.editorElementRef.value = 'editor-element';

		onMountedHook();

		expect(initCKEditor).toHaveBeenCalledWith({
			element: 'editor-element',
			callback: expect.any(Function),
			toolbar: {
				items: ['bold'],
				removeItems: ['italic'],
				shouldNotGroupWhenFull: true,
			},
		});
	});

	it('should set the content after init the editor', () => {
		ckEditorController({ ...defaultProps, value: '<p>text</p>' }, ctx) as View;
		onMountedHook();

		expect(editorMock.setData).toHaveBeenCalledWith('<p>text</p>');
	});

	it('should set the editor to read only mode if disabled', () => {
		ckEditorController({ ...defaultProps, disabled: true }, ctx) as View;
		onMountedHook();

		expect(editorMock.enableReadOnlyMode).toHaveBeenCalledWith('ckeditor-123');
	});

	it('should register events on init', () => {
		ckEditorController(defaultProps, ctx) as View;
		onMountedHook();

		expect(editorMock.model.document.on).toHaveBeenCalledWith('change:data', expect.any(Function));
		expect(editorMock.ui.focusTracker.on).toHaveBeenCalledWith('change:isFocused', expect.any(Function));
		expect(onPluginMock).toHaveBeenNthCalledWith(1, 'show', expect.any(Function));
		expect(onPluginMock).toHaveBeenNthCalledWith(2, 'upload', expect.any(Function));
	});

	it('should attach the toolbar to the editor', () => {
		const view = ckEditorController(defaultProps, ctx) as View;
		view.editorToolbarRef.value = { appendChild: vi.fn() } as unknown as HTMLDivElement;
		onMountedHook();

		expect(view.editorToolbarRef.value.appendChild).toHaveBeenCalledWith('toolbar-element');
	});

	it('should destroy editor on unmounted', () => {
		ckEditorController(defaultProps, ctx) as View;
		onMountedHook();
		onBeforeUnmountHook();

		expect(editorMock.destroy).toHaveBeenCalled();
	});

	it('should initialize with provided value', () => {
		defaultProps.value = 'Hello, world!';
		const view = ckEditorController(defaultProps, ctx) as View;

		expect(view.currentHtml.value).toBe('Hello, world!');
	});

	it('should get editorUniqueId', () => {
		vi.mock('uuid', () => ({ v4: () => '123' }));
		const view = ckEditorController(defaultProps, ctx) as View;

		expect(view.editorUniqueId.value).toBe('ckeditor-123');
	});

	it('should check if editor is focused', () => {
		const view = ckEditorController(defaultProps, ctx) as View;

		expect(view.isEditorFocused.value).toBe(false);
	});

	it('should reinitialize the editor when toolbar changes', async () => {
		const view = ckEditorController(defaultProps, ctx) as View;
		view.editorElementRef.value = 'editor-element';
		onMountedHook();
		watchCallback(['italic'], ['bold']);

		expect(onWatch).toHaveBeenCalled();
		expect(editorMock.destroy).toHaveBeenCalled();
		// Initial init + reinit
		expect(initCKEditor).toHaveBeenCalledTimes(2);
	});

	it('should not reinitialize the editor when toolbar remains the same', async () => {
		const view = ckEditorController(defaultProps, ctx) as View;
		view.editorElementRef.value = 'editor-element';
		onMountedHook();
		watchCallback(['bold'], ['bold']);

		expect(onWatch).toHaveBeenCalled();
		expect(editorMock.destroy).not.toHaveBeenCalled();
		// Only initial init
		expect(initCKEditor).toHaveBeenCalledTimes(1);
	});

	it('should adjust and restore overflow styles when dialog is shown and hidden', () => {
		const onShowMock = vi.fn();
		const onHideMock = vi.fn();
		const editorMockWithDialog = {
			...editorMock,
			plugins: {
				get: (name: string) =>
					name === 'Dialog'
						? {
								on: vi.fn((event, cb) => onShowMock.mockImplementation(cb)),
								once: vi.fn((event, cb) => onHideMock.mockImplementation(cb)),
						  }
						: { on: vi.fn() },
				has: vi.fn(() => false),
			},
		};

		const appTray = document.createElement('div');
		appTray.className = 'app has-tray';
		document.body.appendChild(appTray);

		document.body.style.overflowY = 'scroll';
		appTray.style.overflowY = 'auto';

		(initCKEditor as Mock).mockImplementation(({ callback }) => callback(editorMockWithDialog));
		ckEditorController(defaultProps, ctx);

		onMountedHook();

		onShowMock();
		expect(document.body.style.overflowY).toBe('visible');
		expect(appTray.style.overflowY).toBe('visible');

		onHideMock();
		expect(document.body.style.overflowY).toBe('scroll');
		expect(appTray.style.overflowY).toBe('auto');
	});
});
