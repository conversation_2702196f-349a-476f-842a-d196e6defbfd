import { expect } from 'chai';
import { shallowMount } from '@vue/test-utils';
import TranscodingCompleted from '@/lib/components/Uploader/Preview/TranscodingCompleted.vue';
import VideoModal from '@/lib/components/Video/VideoModal.vue';

describe('TranscodingCompleted', () => {
	it('should return fileId with video- prefix', () => {
		const wrapper = shallowMount(TranscodingCompleted, {
			provide: { lang: { get: () => '' } },
			propsData: {
				file: {
					id: 134,
					image: 'image',
					videoHeight: 200,
					source: 'source',
				},
			},
		});

		const videoModal = wrapper.findComponent(VideoModal);

		expect(wrapper.vm.fileId()).to.equal('video-134');
		expect(videoModal.exists()).to.be.true;
		expect(videoModal.props('fileId')).to.equals('video-134');
	});
});
