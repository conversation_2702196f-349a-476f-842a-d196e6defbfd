<template>
	<div v-click-outside="hideSaveCommentButton" class="comment-new" :class="{ internal: internal }">
		<div class="icon-comment-wrapper">
			<i class="af-icons af-icons-comment"></i>
		</div>

		<text-editor
			v-model="commentContent"
			:disabled="disabled"
			:placeholder="labels['placeholder']"
			:allows-attachments="allowsAttachments"
			:allows-inline-images="true"
			:uploader-object="uploaderObject"
			:uploader-options="uploaderOptions"
			:toolbar-attach-file="canAttachFiles"
			:markdown-guide-label="labels['markdownGuide']"
			:default-language="useGlobal('defaultLanguage')"
			:language="language"
			:translations="translations"
			:links="{}"
			@upload="onUpload"
			@focus="showSaveCommentButton = true"
		/>

		<portal :to="createCommentPortalTarget" :disabled="!createCommentPortalTarget">
			<div v-if="showSaveCommentButton" class="action-buttons-wrapper">
				<button
					type="button"
					class="btn btn-secondary btn-sm"
					:disabled="disabled || pendingUploads > 0"
					@click="createComment"
				>
					{{ labels['saveComment'] }}
				</button>

				<checkbox
					v-if="enableInternalComments"
					name="internalComment"
					:selected="internal"
					:value="internal"
					@change="internal = !internal"
				>
					<template slot="label">{{ labels['internalCheckbox'] }}</template>
				</checkbox>
			</div>
		</portal>

		<!-- Files uploaded -->
		<comment-files
			v-if="allowsAttachments"
			:files="files"
			:read-only="false"
			:uploader="uploaderObject"
			:uploader-options="uploaderOptions"
			@deleted="onDeletedFile"
		/>

		<uploader
			v-if="hasUploaderConfiguration"
			ref="uploader"
			:options="uploaderOptions"
			style="display: none"
			@uploadingFile="onUploadingFile"
			@uploaded="onUploadedFile"
			@imageDataUrl="onImageDataUrl"
			@canceled="onUploadCanceled"
			@completed="onUploadCompleted"
		/>
	</div>
</template>

<script lang="ts">
import CommentFiles from '@/lib/components/Comments/CommentFiles';
import ExpandableTextarea from '@/lib/components/Shared/ExpandableTextarea';
import FileUpload from '@/lib/components/Uploader/FileUpload';
import TextEditor from '@/lib/components/Shared/editor/TextEditor.vue';
import Uploader from '@/lib/components/Uploader/Uploader';
import { defineComponent } from 'vue';
import { useUploadProps } from '@/lib/components/Shared/editor/uploads/uploadsProps';
import { newCommentController } from '@/lib/components/Comments/NewComment.controller';
import { NewCommentView } from '@/lib/components/Comments/NewComment.types';
import { Checkbox } from 'vue-bootstrap';
import CommentActionButtons from '@/lib/components/Comments/CommentActionButtons.vue';
import { useController } from '@/domain/services/Composer';

export default defineComponent({
	components: {
		CommentActionButtons,
		CommentFiles,
		ExpandableTextarea,
		FileUpload,
		TextEditor,
		Uploader,
		Checkbox,
	},

	props: {
		...useUploadProps,

		createUrl: {
			type: String,
			required: true,
		},
		token: {
			type: String,
			required: true,
		},
		labels: {
			type: Object,
			required: true,
		},
		language: {
			type: String,
			default: 'en_GB',
		},
		userId: {
			type: Number,
			required: true,
		},
		translations: {
			type: Object,
			default: () => {},
		},
		createCommentPortalTarget: {
			type: [String, null],
			default: null,
		},
		enableInternalComments: {
			type: Boolean,
			default: false,
		},
	},
	setup: useController(newCommentController, 'newCommentController') as () => NewCommentView,
});
</script>
