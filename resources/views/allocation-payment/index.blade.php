<?php $searching = query_parameters(['entrant', 'status', 'fund', 'payment_method']); ?>

@section('title')
    {!! HTML::pageTitle(trans('allocation-payments.titles.allocation_payments.list')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <allocation-payment-index inline-template>
        <div>
            <allocation-payments-list id="allocation-payments-list" :ids="@js($allocationPaymentIds)" inline-template>
                <div id="searchResults">
                    <div class="selectors island">
                        <div class="selector-title">
                            <div class="mrx">
                                <h1>{{ trans('allocation-payments.titles.allocation_payments.list') }}</h1>
                                @include('partials.holocron.feature-intro-revealer')
                            </div>
                            <div class="inline-block mtm -mlm">
                                @include('partials.page.selectors.season')
                                {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                            </div>
                        </div>
                        @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                            @slot('actions')
                                {!! HTML::exportAction('allocation-payments.export') !!}
                                {!! HTML::broadcast('broadcast.new', 'allocation-payments') !!}
                            @endslot
                        @endcomponent
                    </div>

                    <saved-views-shortcuts-bar
                        area="{{ $area }}"
                        title="{{ trans('search.shortcuts-bar.title') }}"
                        :saved-views="@js($savedViews)"
                        id="saved-views-shortcuts-bar"
                        class="shortcuts-bar-space-above"
                    ></saved-views-shortcuts-bar>

                    <!-- Message -->
                    @include('partials.errors.display')
                    @include('partials.errors.message')

                    <portal-target name="allocation-payment-comments" multiple></portal-target>

                    <div class="row mtm">
                        <div class="col-xs-12 col-lg-6">
                            <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                                <ul class="action-list">
                                    <li>@include('partials.list-actions.delete', ['resource' => 'allocation-payment'])</li>
                                </ul>
                            </list-action-dropdown>
                        </div>


                        <div class="col-xs-12 col-lg-6">
                            <div class="search-info">
                                @include('partials.page.active-filters', ['filters' => Request::all()])
                                @include('partials.page.pagination-info', ['paginator' => $allocationPayments])
                            </div>
                        </div>
                    </div>

                    <div>
                        @if ($allocationPayments->count())
                            @include('search.datatable', ['columnator' => $columnator, 'results' => $allocationPayments->items(), 'class' => 'table markers-table'])
                            <div class="row">
                                <div class="col-xs-12">
                                    @include('partials.page.pagination', ['paginator' => $allocationPayments])
                                </div>
                            </div>
                        @else
                            <div>
                                <p>@lang('allocation-payments.table.no-allocation-payments')</p>
                            </div>
                        @endif
                    </div>
                </div>
            </allocation-payments-list>

            @include('allocation-payment.partials.modal')
        </div>
    </allocation-payment-index>
@stop
