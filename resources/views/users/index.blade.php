<?php $searching = query_parameters(['role', 'entries', 'confirmation', 'season_joined', 'active-in-season', 'language']); ?>

@section('title')
    {!! HTML::pageTitle(trans('users.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <user-list id="user-list" :ids="@js($usersIds)" :translations="@js($translations)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('users.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                        @if (Consumer::can('create', 'Users') || $canDelete)
                            <div class="selector-buttons">
                                {!! Button::link(route('users.invite'), trans('users.buttons.bulk_invite'), ['type' => 'primary']) !!}
                                {!! Button::link(route('users.new'), trans('users.titles.new'), ['type' => 'tertiary']) !!}
                            </div>
                        @endif
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction(['users.export']) !!}
                        {!! HTML::broadcast('broadcast.new', 'users') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                area="{{ $area }}"
                title="{{ trans('search.shortcuts-bar.title') }}"
                :saved-views="@js($savedViews)"
                id="saved-views-shortcuts-bar"
                class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @includewhen(isTrialAccount(), 'partials.trial.info', ['trialMessage' => trans('users.messages.users-limit', ['limit' => config('awardforce.trial.user-limit')])])
            @include('partials.errors.display',['html' => true])
            @include('partials.errors.message')

            @if ($hasIncompleteResults)
                <div class="alert-warning sticky island" role="alert">
                    <div class="icon"><i class="af-icons-md af-icons-md-alert-warning"></i></div>
                    <div class="message">@lang('users.messages.incomplete-results')</div>
                </div>
            @endif

            <portal-target name="assign-role" v-if="roleSelector('assign-role')"></portal-target>
            <portal-target name="remove-role" v-if="roleSelector('remove-role')"></portal-target>
            <portal-target name="destroyer" v-if="destroyerRevealed"></portal-target>
            <portal-target name="form-inviter" v-if="formInviterRevealed" multiple></portal-target>
            <portal-target name="create-document" v-if="createDocumentRevealed" multiple></portal-target>
            <portal-target name="add-member-to-organisation" v-if="addMemberToOrganisationRevealed" multiple></portal-target>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if ($canDelete)
                                <li>
                                    @include('partials.list-actions.role-selector', [
                                            'route' =>  route('user.role.assign'),
                                            'method' => 'POST',
                                            'name' => 'assign-role',
                                            'roles' => $assignableRoles,
                                            'action' => 'add',
                                            'labels' => [
                                                'button'    => trans('users.buttons.assign_role'),
                                                'cancel'    =>  trans('users.buttons.done_assigning')
                                                ]
                                            ])
                                </li>
                                <li>
                                    @include('partials.list-actions.role-selector', [
                                            'route' =>  route('user.role.remove'),
                                            'method' => 'DELETE',
                                            'name' => 'remove-role',
                                            'roles' => $removableRoles,
                                            'action' => 'remove',
                                            'labels' => [
                                                'button' => trans('users.buttons.remove_role'),
                                                'cancel' => trans('users.buttons.done_removing')
                                                ]
                                            ])
                                </li>

                                @if (!trashed_filter_active() && feature_enabled('documents') && Consumer::can('create', 'Documents'))
                                    <li>@include('partials.list-actions.create-document', ['resource' => 'user'])</li>
                                @endif

                                <li class="divider"></li>
                                @if($showAddToOrganisation)
                                    <li>
                                        @include('partials.list-actions.add-to-organisation')
                                    </li>
                                @endif
                                <li>@include('partials.list-actions.delete', ['resource' => 'user'])</li>
                                @if($hasFileFields || $hasDocuments)
                                    <li>@include('partials.list-actions.download', ['resource' => 'user'])</li>
                                @endif
                                @if(Consumer::isOwner())
                                    <li class="divider"></li>
                                    <li>@include('partials.list-actions.destroy', ['resource' => 'user'])</li>
                                @endif
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => request()->all(), 'context' => ['resource' => 'Users']])
                        @include('partials.page.pagination-info', ['paginator' => $users])
                    </div>
                </div>
            </div>

            <div>
                @if ($users->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $users->items(), 'class' => 'users-table markers-table'])
                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $users])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('users.table.empty')</p>
                    </div>
                @endif

                @if (Consumer::can('create', 'Users'))
                    <div class="row island">
                        <div class="col-xs-12">
                            <a href="{{ route('user.import') }}"
                               class="btn btn-tertiary">{{ trans('users.table.import') }}</a>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </user-list>
@stop
