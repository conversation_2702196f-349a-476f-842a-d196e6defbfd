<?php $searching = query_parameters(['status', 'test']); ?>

@section('title')
    {!! HTML::pageTitle(trans('orders.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <order-list id="order-list" :ids="@js($ordersIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="order-list-title">
                    <div class="selector-title">
                        <div class="mrx">
                            <h1>{!! trans('orders.titles.main') !!}</h1>
                            @include('partials.holocron.feature-intro-revealer')
                        </div>
                        <div class="inline-block mtm -mlm">
                            @include('partials.page.selectors.season')
                            {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                        </div>
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction(['orders.export', 'order-items.export']) !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.delete', ['resource' => 'order'])</li>
                        </ul>
                    </list-action-dropdown>
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => request()->all()])
                        @include('partials.page.pagination-info', ['paginator' => $orders])
                    </div>
                </div>

            </div>

            <div>
                @if ($orders->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $orders->items(), 'class' => 'table markers-table order'])
                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $orders])
                        </div>
                    </div>
                @else
                    <div>
                        <p>{{ trans('orders.table.empty') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </order-list>
@stop
