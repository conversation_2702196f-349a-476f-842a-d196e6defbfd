@section('title')
    {!! HTML::pageTitle(trans('integrations.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <integration-list id="integration-list" :ids="@js($integrationIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('integrations.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    <div class="selector-buttons">
                        {!! Button::link(route('integration.new'), trans('integrations.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => false, 'disableAdvanced' => true]))
                @endcomponent
            </div>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'integration'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'integration'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $integrations])
                    </div>
                </div>
            </div>
        @include('partials.errors.display')
        @include('partials.errors.message')

        <!-- Result set -->
            <div>
                @if ($integrations->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $integrations->items(), 'class' => 'assignments-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $integrations])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('integrations.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </integration-list>
@stop
