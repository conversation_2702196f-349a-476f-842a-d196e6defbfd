@section('title')
    {!! HTML::pageTitle(trans('form.titles.list')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    <div id="forms-list">
        <div class="row island">
            <div class="col-xs-12">
                <div class="selector-title">
                    <div class="title mrx">
                        <h1>{{ trans('form.titles.list') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @if (Consumer::can('create', 'Forms'))
                        <div class="selector-buttons">
                            {!! Button::link(route('forms.new', ['type' => 'entry']), trans('form.titles.new.entry'), ['type' => 'primary']) !!}
                            @if($showGrantReportForms)
                                {!! Button::link($newGrantReportFormLink, trans('form.titles.new.report'), ['type' => 'primary']) !!}
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        @includewhen($info = session('formLimit'), 'partials.trial.info', ['trialMessage' => $info])
        @include('partials.errors.message')
        @include('partials.errors.display')

        <div class="row">
            <div class="col-xs-12">
                <forms-list
                    :forms="@js($forms, JSON_NUMERIC_CHECK)"
                    :ids="@js($formIds)"
                    :routes="@jsObject($routes)"
                    :can-create="{{ boolean_string_value($canCreate) }}"
                    :can-update="{{ boolean_string_value($canUpdate) }}"
                    :can-delete="{{ boolean_string_value($canDelete) }}"
                    :translations="@js($translations)">
                </forms-list>
            </div>
        </div>
    </div>

    <div class="hidden form-copy-completed"></div>
@stop
