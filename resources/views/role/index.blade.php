@section('title')
    {!! $title = HTML::pageTitle(trans('roles.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <role-list id="role-list" :ids="@js($rolesIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('roles.titles.main') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @if($canCreate)
                        <div class="selector-buttons">
                            {!! Button::link(route('role.new'), trans('roles.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                        </div>
                    @endif
                </div>
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            <!-- Message -->
            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'role'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'role'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $roles])
                    </div>
                </div>
            </div>

            <div>
                @if ($roles->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $roles->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $roles])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('roles.table.empty')</p>
                    </div>
                @endif
            </div>

        </div>
    </role-list>
@stop
