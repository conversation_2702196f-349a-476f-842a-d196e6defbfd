@section('title')
    {!! HTML::pageTitle(trans('payment-methods.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <payment-methods-list id="payment-methods-list" :ids="@js($paymentMethodsIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('payment-methods.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    <div class="selector-buttons">
                        <div class="selector-buttons buttons-spacer island">
                            @if (Consumer::isManager())
                                {!! Button::link(route('payment-method.new'), trans('payment-methods.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                            @endif
                        </div>
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => true, 'disableAdvanced' => true])
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                area="{{ $area }}"
                title="{{ trans('search.shortcuts-bar.title') }}"
                :saved-views="@js($savedViews)"
                id="saved-views-shortcuts-bar"
                class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'payment-method'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'payment-method'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $paymentMethods])
                    </div>
                </div>
            </div>

            <!-- Result set -->
            <div>
                @if ($paymentMethods->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $paymentMethods->items(), 'class' => 'table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $paymentMethods])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('payment-methods.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </payment-methods-list>
@stop
