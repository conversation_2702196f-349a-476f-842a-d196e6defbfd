<?php $searching = query_parameters(['manager', 'status']); ?>

@section('title')
    {!! HTML::pageTitle(sentence_case(Term::plural('chapter'))) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <chapter-list id="chapter-list" :ids="@js($chaptersIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ sentence_case(Term::plural('chapter')) }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @if (Consumer::can('create', 'Chapters'))
                        <div class="selector-buttons">
                            {!! Button::link(route('chapter.add'), sentence_case(trans('chapters.titles.add')), ['type' => 'primary', 'size' => 'lg']) !!}
                        </div>
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('chapter.export') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (!trashed_filter_active())
                                <li>@include('partials.list-actions.copy', ['resource' => 'chapter'])</li>
                            @endif
                            <li>@include('partials.list-actions.delete', ['resource' => 'chapter'])</li>
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => request()->all()])
                        @include('partials.page.pagination-info', ['paginator' => $chapters])
                    </div>
                </div>
            </div>

        @include('partials.errors.display')
        @include('partials.errors.message')

        <!-- Result set -->
            <div>
                @if ($chapters->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $chapters->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $chapters])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('chapters.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </chapter-list>
@stop
