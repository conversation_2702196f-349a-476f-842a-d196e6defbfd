@section('title')
    {!! HTML::pageTitle(trans('payments.prices.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <div class="row island">
        <div class="col-xs-12">
            <div class="selector-title">
                <div class="mrx">
                    <h1>{{ trans('payments.prices.titles.main') }}</h1>
                    @include('partials.holocron.feature-intro-revealer')
                </div>
                <div class="inline-block mtm -mlm">
                    @include('partials.page.selectors.season')
                </div>
            </div>
        </div>
    </div>

    @include('partials.errors.message')
    @include('partials.errors.display')

    <div class="row">
        <div class="col-xs-12">

            @if (array_get($settings, 'paid-entries'))
                <div class="row island">
                    <div class="col-xs-12">
                        @if ($supportedCurrencies->count())
                            <div class="buttons">
                                {!! Button::link(route('price.new'), trans('payments.prices.titles.new'), ['type' => 'secondary', 'size' => null]) !!}
                            </div>
                        @else
                            <div class="alert-info sticky island" role="alert">
                                <div class="icon">
                                    <i class="af-icons-md af-icons-md-alert-info"></i>
                                </div>
                                <div class="message">
                                    @lang('payments.currencies.alerts.no_currency', ['currency_link' => route('currency')])
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div>
                    @if ($prices->count())
                        <table class="table marker-table">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>{{ trans('payments.prices.table.columns.title') }}</th>
                                    <th>{{ trans('payments.prices.table.columns.type') }}</th>
                                    <th>{{ trans('payments.prices.table.columns.selectable') }}</th>
                                    @foreach ($supportedCurrencies as $currency)
                                        <th>{{ $currency->code }}</th>
                                    @endforeach
                                    <th>{{ trans('payments.prices.table.columns.updated') }}</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($prices as $price)
                                    <tr>
                                        <td>
                                            @include('price.partials.action-overflow')
                                        </td>
                                        <td>
                                            <a href="{{ route('price.show', $price->slug) }}">{{ $price->title }}</a>
                                            @if ($price->default)
                                                (@lang('payments.prices.default'))
                                            @endif
                                        </td>
                                        <td>@lang('payments.prices.types.'.$price->type)</td>
                                        <td>{{ HTML::priceVisibility($price->selectable) }}</td>
                                        @foreach ($supportedCurrencies as $currency)
                                            <th>{{ default_amount($price->amounts, $currency->code) }}</th>
                                        @endforeach
                                        <td>{{ HTML::relativeTime($price->updatedAt) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div>
                            <p>@lang('payments.prices.table.empty')</p>
                        </div>
                    @endif
                </div>
            @else
                @lang('payments.disabled', ['url' => route('payment.general')])
            @endif

        </div>
    </div>
@stop
