@section('title')
    {!! HTML::pageTitle(trans('rounds.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <round-list id="round-list" :ids="@js($roundsIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('rounds.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @includeWhen($canCreate, 'partials.list-actions.add-resource', ['label' => trans('rounds.titles.add'), 'route' => route('round.add')])
                </div>
            </div>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'round'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'round'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $rounds])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($rounds->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $rounds->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $rounds])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('rounds.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </round-list>
@stop
