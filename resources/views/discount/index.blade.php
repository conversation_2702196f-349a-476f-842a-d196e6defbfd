@section('title')
    {!! HTML::pageTitle(trans('payments.discounts.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <div class="row island">
        <div class="col-xs-12">
            <div class="selector-title">
                <div class="mrx">
                    <h1>{{ trans('payments.discounts.titles.main') }}</h1>
                    @include('partials.holocron.feature-intro-revealer')
                </div>
                <div class="inline-block mtm -mlm">
                    @include('partials.page.selectors.season')
                </div>
            </div>
        </div>
    </div>

    @include('partials.errors.message')
    @include('partials.errors.display')

    <div class="row">
        <div class="col-xs-12">

            @if (array_get($settings, 'paid-entries'))
                <div class="row island">
                    <div class="col-xs-12">
                        <div class="buttons">
                            {!! Button::link(route('discount.new'), trans('payments.discounts.titles.new'), ['type' => 'secondary', 'size' => null]) !!}
                        </div>
                    </div>
                </div>

                <div>
                    @if ($discounts->count())
                        <table class="table marker-table">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>{{ trans('payments.discounts.table.columns.code') }}</th>
                                    <th>{{ trans('payments.discounts.table.columns.type') }}</th>
                                    <th>{{ trans('payments.discounts.form.use.maximum_uses') }}</th>
                                    <th>{{ trans('payments.discounts.form.use.maximum_uses_per_user') }}</th>
                                    <th>{{ trans('payments.discounts.table.columns.quantity_used') }}</th>
                                    <th>{{ trans('payments.discounts.table.columns.categories.heading') }}</th>
                                    <th>{{ trans('payments.discounts.table.columns.updated') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($discounts as $i => $discount)
                                    <tr @if ($i % 2 == 1) class="even"@endif>
                                         <td>
                                            @include('discount.partials.action-overflow', compact('discount'))
                                        </td>
                                        <td><a href="{{ route('discount.edit', $discount->slug) }}">{{ $discount->code }}</a></td>
                                        <td>{{ trans('payments.discounts.types.'.$discount->type) }}</td>
                                        <td>{{ is_numeric($discount->maximumUses) ? $discount->maximumUses : trans('payments.discounts.form.use.unlimited') }}</td>
                                        <td>{{ is_numeric($discount->maximumUsesPerUser) ? $discount->maximumUsesPerUser : trans('payments.discounts.form.use.unlimited') }}</td>
                                        <td>{{ $discount->usersCount }}</td>
                                        <td>
                                            @if ($discount->appliesToAllCategories())
                                                @lang('payments.discounts.table.columns.categories.all')
                                            @else
                                                {!! $discount->categories->count() !!}
                                            @endif
                                        </td>
                                        <td>{!! HTML::relativeTime($discount->updatedAt) !!}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    @else
                        <div>
                            <p>@lang('payments.discounts.table.empty')</p>
                        </div>
                    @endif
                </div>
            @else
                @lang('payments.disabled', ['url' => route('payment.general')])
            @endif

        </div>
    </div>
@stop
