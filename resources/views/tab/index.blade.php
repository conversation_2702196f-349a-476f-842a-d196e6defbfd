<?php $searching = query_parameters(['tab_type']); ?>

@section('title')
    {!! HTML::pageTitle(trans('tabs.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <tab-list id="tab-list" :ids="@js($tabsIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('tabs.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter(['entry', 'report']) !!}
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>

                    @if (Consumer::can('create', 'Fields'))
                        @include('partials.list-actions.add-resource', ['label' => trans('tabs.titles.new'), 'route' => route('tab.new'), 'allowedTypes' => ['entry', 'report']])
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('tabs.export') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'tab'])</li>
                            @else
                                <li>@include('partials.list-actions.copy', ['resource' => 'tab'])</li>
                                <li>@include('partials.list-actions.delete', ['resource' => 'tab'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $tabs])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($tabs->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $tabs->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $tabs])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('tabs.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </tab-list>
@stop
