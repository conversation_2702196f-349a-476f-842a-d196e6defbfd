@section('title')
    {!! HTML::pageTitle(sentence_case(trans('dashboard.titles.dashboard'))) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    @include('partials.errors.message')
    @include('users.confirm')

    <h1 class="sr-only">@lang('dashboard.titles.dashboard')</h1>
    <div id="searchResults"{!! query_parameters(['status']) ? ' class="courteous"' : '' !!}>

        <div class="row row-relative island row-selectors">
            <div class="selector-title">
                <div class="mrx">
                    <h1>{{ trans('dashboard.widgets.round_activity.title') }}</h1>
                    @include('partials.holocron.feature-intro-revealer')
                </div>
                <a href="{{ route('round.index') }}" class="round-settings">@lang('dashboard.widgets.round_activity.settings')</a>
            </div>
        </div>

        <div class="row widget-row">
            <div class="round-activity-widget-container">
                {!! $roundActivityWidget->render() !!}
            </div>
        </div>

        <div class="row row-relative island row-selectors" id="selectors">
            <div class="selector-title">
                <div class="mrx">
                    <h1>{{ sentence_case(Term::plural('entry')) }}</h1>
                </div>
                <div class="inline-block mtm -mlm">
                    @include('partials.page.selectors.season')
                    {!! html()->formFilter() !!}
                </div>
            </div>
        </div>

        <div class="widgets">
            <div class="row widget-row">
                @foreach ($entryWidgets as $widget)
                    {!! $widget->render() !!}
                @endforeach
            </div>
        </div>

        <div class="row row-relative island">
            <div class="col-xs-12">
                <div class="selector-title">
                    <h1>{{ trans('dashboard.titles.users') }}</h1>
                </div>
            </div>
        </div>

        <div class="widgets">
            <div class="row widget-row">
                @foreach ($userWidgets as $widget)
                    {!! $widget->render() !!}
                @endforeach
            </div>
        </div>

        <div class="widgets">
            <div class="row widget-row">
                @foreach ($usageWidgets as $widget)
                    {!! $widget->render() !!}
                @endforeach
            </div>
        </div>
    </div>
@stop
