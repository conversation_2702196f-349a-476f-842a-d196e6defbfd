<?php $searching = query_parameters(['status', 'chapter', 'parent']); ?>

@section('title')
    {!! HTML::pageTitle(trans('category.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <category-list id="category-list" :ids="@js($categoryIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('category.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @include('partials.list-actions.add-resource', ['label' => trans('category.titles.new'), 'route' => route('category.new')])
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('categories.export') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  :area="@js($area)"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (!trashed_filter_active())
                                <li>@include('partials.list-actions.copy', ['resource' => 'category'])</li>
                            @endif
                            <li>@include('partials.list-actions.delete', ['resource' => 'category'])</li>
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $categories])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($categories->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $categories->items(), 'class' => 'table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $categories])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('category.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </category-list>
@stop
