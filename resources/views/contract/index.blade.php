<?php $searching = query_parameters(['status', 'contract_template', 'entry']); ?>

@section('title')
    {!! HTML::pageTitle(trans('contract.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <contract-list id="contract-list" :ids="@js($contractIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('contract.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                      {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction(['contracts.export']) !!}
                        {!! HTML::broadcast('broadcast.new', 'contract') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.download', ['resource' => 'contract'])</li>
                            @if (Consumer::can('delete', 'ContractAll'))
                                <li>@include('partials.list-actions.delete', ['resource' => 'contract'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>

                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => request()->all()])
                        @include('partials.page.pagination-info', ['paginator' => $contracts])
                    </div>
                </div>
            </div>

            <!-- Result set -->
            <div >
                @if ($contracts->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $contracts->items(), 'class' => 'assignments-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $contracts])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('contract.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </contract-list>
    {!! Button::link(route('content-block.new', ['key' => 'contract']), trans('contract.titles.new_template'), ['type' => 'tertiary', 'size' => 'sm']) !!}
@endsection
