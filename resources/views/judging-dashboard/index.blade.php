@section('title')
    {!! HTML::pageTitle(trans('form.titles.list')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <div id="judging-dashboard-vue">
        <div class="row island">
            <div class="col-xs-12">
                <div class="title mrx">
                    <h1>{{ trans('judging.titles.dashboard') }}</h1>
                    @include('partials.holocron.feature-intro-revealer')
                </div>
                <div class="selector-title">
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12">
                <judging-dashboard
                        id="judging-dashboard"
                        :score-sets="@js($scoreSetList)"
                        :routes="@jsObject($routes)"
                        :stats="@jsObject($stats)"
                        :rounds="@jsObject($roundList)"
                        :judging-progress="@jsObject($judgingProgress)"
                        :menu="@js($judgingMenu ?? [])"
                        :permissions="@js($permissions)"
                        language="{{ Consumer::languageCode() }}"
                        default-language="{{ CurrentAccount::defaultLanguage()->code }}"
                        :translations="@js($translations)"
                        :available-forms="@js($availableForms)"
                        :multiform="{{ boolean_string_value($isMultiform)}}"
                ></judging-dashboard>
            </div>
        </div>
    </div>

@stop
