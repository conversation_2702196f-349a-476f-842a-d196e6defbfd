<?php $searching = query_parameters([]); ?>
@section('title')
    {!! HTML::pageTitle(trans('exports.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    <export-layout-list id="export-layout-list" :ids="@js($exportLayoutIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('exports.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    <div class="selector-buttons">
                        {!! Button::link(route('export.new'), trans('exports.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', ['searching' => $searching, 'columnator' => $columnator, 'area' => $area,'disableAdvanced' => true])@endcomponent
            </div>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'export'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'export'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $exportLayouts])
                    </div>
                </div>
            </div>

            <!-- Result set -->
            <div>
                @if ($exportLayouts->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $exportLayouts->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $exportLayouts])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('exports.table.empty')</p>
                    </div>
                @endif
            </div>

        </div>
    </export-layout-list>
@stop
