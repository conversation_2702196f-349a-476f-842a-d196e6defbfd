@php
    $displayAttachment = !isset($attachmentDownload) || $attachmentDownload;
@endphp

@if (is_image($file->file))
    <div class="card">
        @if (is_resizable($file))
            <div class="preview-container">
                <a href="{{ imgix($file->file, $file->original, config('ui.images.gallery.large')) }}" target="_blank" rel="noopener noreferrer" class="strip" data-strip-group="attached-images">
                    <div class="preview"><img src="{{ imgix($file->file, $file->original, config('ui.images.gallery.large')) }}" class="img-responsive" alt="{{ $file->original }}"></div>
                </a>
            </div>
        @else
            <div class="preview-container no-preview">
                <a href="{{ cloud_asset_url($file->file, true, filename: $file->original) }}" target="_blank" rel="noopener noreferrer" class="ignore">
                    @include('entry.common.cards.previews.generic-file-preview', ['file' => $file->file])
                </a>
            </div>
        @endif

        @if (isset($displayMetadata) && $displayMetadata)
            <file-metadata :file="@js($file->id)"></file-metadata>
        @endif

        @if ($displayAttachment)
            @include('entry.common.cards.sections.download', ['file' => $file])
        @endif
    </div>
@elseif (card_type($file) === 'pdf')
    <div class="download pdf-preview">
        @if($displayAttachment)
            <a href="{{ cloud_asset_url($file->file, true, filename: $file->original) }}" target="_blank" rel="noopener noreferrer" class="pdf-link ignore">
                <i class="af-icons af-icons-pdf"></i>
                <span>
                    @if ($anonymiseAttachments ?? false)
                        {{ trans('files.download') }}
                    @else
                        {{ $file->original }}
                    @endif
                    (@lang('files.buttons.size_download', ['size' => $file ? format_size($file->size) : '']))
                </span>
            </a>
        @endif
    </div>
    @include('judging.partials.plagiarism-scan', ['scan' => $plagiarismScans['file'][$file->id] ?? null, 'pdf' => true])

    @if (is_resizable($file))
        <pdf-viewer
            name="{{ get_file_name($file->original) }}"
            src="{{ cloud_asset_url($file->file, true) . ($displayAttachment ? '' : '#toolbar=0') }}"
        ></pdf-viewer>
    @endif
@elseif (card_type($file) === 'video')
    <div class="card">
        <div class="preview-container">
            <div class="preview">
                <video-player
                    file-id="video-{{ $file->id }}"
                    source="{{ cloud_asset_url($file->transcodePlaylist()) }}"
                    caption="{{ $file->caption ? cloud_asset_url($file->caption->location('file.vtt')) : '' }}"
                    aspect-ratio="{{ $file->videoAspectRatio() }}"
                    poster="{{ imgix($file->transcodeThumbnail()) }}"
                ></video-player>
            </div>
        </div>
        <div class="card video-attachment-card">
            @if ($displayAttachment)
                @include('entry.common.cards.sections.download', ['file' => $file])
            @endif
        </div>
    </div>
@else
    <div class="card">
        {{-- Preview --}}
        @if (card_type($file) === 'audio')
            @include('entry.common.cards.previews.audio', ['preview' => $file])
        @else
            <div class="preview-container no-preview">
                <a href="{{ cloud_asset_url($file->file, true, filename: $file->original) }}" target="_blank" rel="noopener noreferrer" class="ignore">
                    @include('entry.common.cards.previews.generic-file-preview', ['file' => $file->file])
                </a>
            </div>
            @include('judging.partials.plagiarism-scan', ['scan' => $plagiarismScans['file'][$file->id] ?? null])
        @endif

        {{-- Audio player --}}
        @if (card_type($file) === 'audio')
            @include('entry.common.cards.sections.audio-player', ['file' => $file])
        @endif

        {{-- Download --}}
        @if ($displayAttachment)
            @include('entry.common.cards.sections.download', ['file' => $file])
        @endif
    </div>
    <br>
@endif
