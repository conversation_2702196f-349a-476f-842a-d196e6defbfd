@section('title')
    {!! HTML::pageTitle(trans('payments.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title title-payments selector-title">
                <div class="mrx">
                    <h1>{{ trans('payments.titles.main') }}</h1>
                    @include('partials.holocron.feature-intro-revealer')
                    <div class="onoffswitch-container" data-warning="{{ sentence_case(trans('payments.info.payments_off_warning')) }}">
                        {!! html()->form('put', route('payment.toggle'))->open() !!}
                        @include('html.buttons.onoffswitch', [
                            'name' => 'paid-entries',
                            'id' => 'paid-entries',
                            'checked' => array_get($settings, 'paid-entries'),
                        ])
                        {!! html()->form()->close() !!}
                    </div>
                </div>
                @if (array_get($settings, 'paid-entries'))
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                    </div>
                @endif
            </div>
        </div>
    </div>

    @include('partials.errors.display')
    @include('partials.errors.message')

    <div class="form-settings">
        {!! html()->form('put', route('payment.general.update'))->open() !!}
        @if (array_get($settings, 'paid-entries'))
            <div class="payment-general-settings">
                <div class="row">
                    <div class="col-xs-12 col-md-4">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h4>@lang('payments.form.entry_payment.title')</h4>
                                </div>
                                @foreach (['submit', 'start'] as $value)
                                    <div class="form-group">
                                        <div class="radio styled">
                                            {!! html()->radio('setting[entry-payment]', null, $value)->attributes(['id' => 'radio-'.$value])->checked( array_get($settings, 'entry-payment', 'submit') == $value) !!}
                                            <label for="{{ 'radio-'.$value }}">{{ trans('payments.form.entry_payment.'.$value) }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="panel panel-default">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h4>@lang('payments.titles.cart_options')</h4>
                                </div>
                                <div class="mbl">
                                    <p>{!! html()->label(trans('payments.form.free-cart-billing-details.label'), 'free-cart-billing-details') !!}</p>
                                    @foreach (['required', 'optional', 'hidden'] as $value)
                                        <div class="form-group">
                                            <div class="radio styled">
                                                {!! html()->radio('setting[free-cart-billing-details]', null, $value)->attributes(['id' => 'radio-'.$value])->checked(array_get($settings, 'free-cart-billing-details', 'optional') == $value) !!}
                                                <label for="{{ 'radio-'.$value }}">{{ trans("payments.form.free-cart-billing-details.{$value}") }}</label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                <div>
                                    <p>{!! html()->label(trans('payments.form.display-state-in-cart.label'), 'display-state-in-cart') !!}</p>
                                    <div class="form-group">
                                        <div class="radio styled">
                                            {!! html()->radio('setting[display-state-in-cart]', null, 1)->attributes(['id' => 'display-state-in-cart-on'])->checked(array_get($settings, 'display-state-in-cart') == 1) !!}
                                            <label for="display-state-in-cart-on">{{ trans('payments.form.display-state-in-cart.yes') }}</label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="radio styled">
                                            {!! html()->radio('setting[display-state-in-cart]', null, 0)->attributes(['id' => 'display-state-in-cart-off'])->checked(array_get($settings, 'display-state-in-cart') == 0) !!}
                                            <label for="display-state-in-cart-off">{{ trans('payments.form.display-state-in-cart.no') }}</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h3>{{ trans('payments.titles.billing_information') }}</h3>
                        <div class="form-group">
                            {!! html()->label(trans('payments.form.legal_name.label'), 'legal-name') !!}
                            {!! html()->text('setting[legal-name]', array_get($settings, 'legal-name'))->attributes(['class' => 'form-control']) !!}
                        </div>
                        <div class="form-group">
                            {!! html()->label(trans('payments.form.organisation_address.label'), 'organisation-address') !!}
                            {!! html()->textarea('setting[organisation-address]', array_get($settings, 'organisation-address'))->attributes(['class' => 'form-control', 'rows' => '6']) !!}
                        </div>
                        <div class="form-group">
                            {!! form_label('tax-number', trans('payments.form.tax_number.label')) !!}
                            {!! html()->text('setting[tax-number]', array_get($settings, 'tax-number'))->attributes(['class' => 'form-control']) !!}
                        </div>
                        <div class="form-group">
                            <div class="cards clearfix">
                                {!! html()->upload(trans('payments.form.files.organisation_logo.label'), "setting[organisation-logo]", array_get($files, 'organisation-logo'), $uploader) !!}
                                <span class="help-text">{{ trans('payments.form.files.organisation_logo.hint') }}</span>
                            </div>
                        </div>
                        <div class="form-group">
                            {!! form_label('next-invoice-number', trans('payments.form.next_invoice_number.label')) !!}
                            {!! html()->text('setting[next-invoice-number]', $nextInvoiceNumber)->attributes(['class' => 'form-control']) !!}
                        </div>
                    </div>
                    <div class="col-xs-12 col-md-4">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h4>
                                        <label for="accept-credit-cards">{{ trans('payments.titles.payment_methods') }}</label>
                                    </h4>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox styled">
                                        {!! html()->hidden('setting[accept-credit-cards]', 0) !!}
                                        {!! html()->checkbox('setting[accept-credit-cards]', array_get($settings, 'accept-credit-cards'), 1)->attributes(['id' => 'accept-credit-cards']) !!}
                                        <label for="accept-credit-cards">
                                            {{ trans('payments.form.accept_credit_cards.label') }}<br/>
                                            {{ trans('payments.form.accept_credit_cards.help') }}
                                        </label>
                                    </div>

                                    <div class="checkbox styled">
                                        {!! html()->hidden('setting[accept-invoice]', 0) !!}
                                        {!! html()->checkbox('setting[accept-invoice]', array_get($settings, 'accept-invoice'), 1)->attributes(['id' => 'accept-invoice']) !!}
                                        <label for="accept-invoice">
                                            {{ trans('payments.form.accept_invoice.label') }}<br/>
                                            {{ trans('payments.form.accept_invoice.help') }}
                                        </label>
                                    </div>
                                    <div class="invoice-instructions">
                                        {{ trans('payments.form.invoice_instructions.label') }}
                                        {!! html()->textarea('setting[invoice-instructions]', array_get($settings, 'invoice-instructions'))->attributes(['class' => 'form-control', 'rows' => '6', 'data-autosize-on' => 'true']) !!}
                                    </div>

                                    <div class="checkbox styled">
                                        {!! html()->hidden('setting[accept-paypal]', 0) !!}
                                        {!! html()->checkbox('setting[accept-paypal]', array_get($settings, 'accept-paypal'), 1)->attributes(['id' => 'accept-paypal']) !!}
                                        <label for="accept-paypal">
                                            {{ trans('payments.form.accept_paypal.label') }}<br/>
                                            {{ trans('payments.form.accept_paypal.help') }}
                                        </label>
                                    </div>
                                    <div class="paypal-settings">
                                        <div class="form-group">
                                            {!! html()->label(trans('payments.form.paypal_express_username.label'), 'paypal-express-username') !!}
                                            {!! html()->text('setting[paypal-express-username]', array_get($settings, 'paypal-express-username'))->attributes(['class' => 'form-control']) !!}
                                        </div>
                                        <div class="form-group">
                                            {!! html()->label(trans('payments.form.paypal_express_password.label'), 'paypal-express-password') !!}
                                            {!! html()->text('setting[paypal-express-password]', array_get($settings, 'paypal-express-password'))->attributes(['class' => 'form-control']) !!}
                                        </div>
                                        <div class="form-group">
                                            {!! html()->label(trans('payments.form.paypal_express_signature.label'), 'paypal-express-signature') !!}
                                            {!! html()->text('setting[paypal-express-signature]', array_get($settings, 'paypal-express-signature'))->attributes(['class' => 'form-control']) !!}
                                        </div>
                                    </div>

                                    @php
                                        $stripeEnabled = \Illuminate\Support\Arr::get($settings, 'payment-gateway') == 'stripe_connect';
                                    @endphp
                                    <label class="strong">@lang('payments.form.stripe_gateway_only')</label>
                                    <div class="checkbox styled">
                                        {!! html()->hidden('setting[accept-ideal]', 0) !!}
                                        {!! html()->checkbox('setting[accept-ideal]', $stripeEnabled & array_get($settings, 'accept-ideal'), 1)->attributes(['id' => 'accept-ideal', !$stripeEnabled ? 'disabled' : '']) !!}
                                        <label for="accept-ideal">
                                            {{ trans('payments.form.accept_ideal.label') }}
                                        </label>
                                    </div>
                                    <div class="checkbox styled">
                                        {!! html()->hidden('setting[accept-alipay]', 0) !!}
                                        {!! html()->checkbox('setting[accept-alipay]', $stripeEnabled & array_get($settings, 'accept-alipay'), 1)->attributes(['id' => 'accept-alipay', !$stripeEnabled ? 'disabled' : '']) !!}
                                        <label for="accept-alipay">
                                            {{ trans('payments.form.accept_alipay.label') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            {!! html()->label(trans('payments.form.payment_identifier.label'), 'payment-identifier') !!}
                            {!! html()->text('setting[payment-identifier]', array_get($settings, 'payment-identifier'))->attributes(['class' => 'form-control']) !!}
                        </div>

                        @if($paymentDescriptionSupported)
                            <merge-fields-standalone inline-template>
                            <merge-fields
                                :merge-fields="@js($mergeFields)"
                                help-text="{{ trans('payments.form.payment_gateway_description.help') }}"
                            >
                                <template #default="useMergeFields">
                                    {!! html()->label(trans_merge('shared.description', 'miscellaneous.optional'), 'setting[payment-gateway-description]') !!}
                                    {!!
                                        html()->textarea('setting[payment-gateway-description]', array_get($settings, 'payment-gateway-description', ''))
                                            ->attributes([
                                                'class' => 'form-control',
                                                'rows' => '6',
                                                '@keyup' => '(element) => useMergeFields.setCaretPosition(element)',
                                                '@focus' => '(element) => useMergeFields.setCaretPosition(element)'
                                            ])
                                    !!}
                                </template>
                            </merge-fields>
                            </merge-fields-standalone>
                        @endif
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h4>@lang('payments.form.test_mode.label')</h4>
                                </div>
                                <div class="form-group">
                                    <div class="radio styled">
                                        {!! html()->radio('setting[payment-test-mode]', null, 1)->attributes(['id' => 'radio-test-on'])->checked(array_get($settings, 'payment-test-mode') == 1) !!}
                                        <label for="radio-test-on">{{ trans('payments.form.test_mode_on.label') }}</label>
                                    </div>
                                    <div class="radio styled">
                                        {!! html()->radio('setting[payment-test-mode]', null, 0)->attributes(['id' => 'radio-test-off'])->checked(array_get($settings, 'payment-test-mode') == 0) !!}
                                        <label for="radio-test-off">{{ trans('payments.form.test_mode_off.label') }}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-md-4">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h4>{{ trans('payments.titles.processing_fees') }}</h4>
                                </div>
                                <p>{{ trans('payments.info.payment_processing') }}</p>
                                <div class="form-group">
                                    <div class="checkbox styled">
                                        {!! html()->hidden('setting[levy-processing-fee]', 0) !!}
                                        {!! html()->checkbox('setting[levy-processing-fee]', array_get($settings, 'levy-processing-fee'), 1)->attributes(['id' => 'levy-processing-fee']) !!}
                                        <label for="levy-processing-fee">{{ trans('payments.form.levy_processing_fee.label') }}</label>
                                    </div>
                                </div>
                                <div class="processing-fees">
                                    @foreach(Config::get('payments.optional_processing_fees') as $key => $value)
                                        <div class="form-group">
                                            {!! html()->label(trans("payments.form.{$value}_processing_fee.label"), "{$value}-processing-fee") !!}
                                            <div class="input-group">
                                                {!! html()->text("setting[{$value}-processing-fee]", array_get($settings, "{$value}-processing-fee"))->attributes(['class' => 'form-control']) !!}
                                                <span class="input-group-addon">%</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {!! $uploader->script() !!}
            </div>

            <div class="row">
                <div class="col-xs-12">
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-lg uploading-disable">
                            {!! trans('buttons.save') !!}
                        </button>
                    </div>
                </div>
            </div>
        @endif
        {!! html()->form()->close() !!}
    </div>
@stop
