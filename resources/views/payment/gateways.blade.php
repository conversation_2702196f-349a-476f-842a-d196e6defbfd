@section('title')
    {!! HTML::pageTitle(trans('payments.tabs.gateways')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title title-payments selector-title mrx">
                <h1>{{ trans('payments.tabs.gateways') }}</h1>
                @include('partials.holocron.feature-intro-revealer')
                @if (array_get($settings, 'paid-entries'))
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                    </div>
                @endif
            </div>
        </div>
    </div>

    @include('partials.errors.display')
    @include('partials.errors.message')

    <div class="form-settings">
        {!! html()->form('put', route('payment.gateways.update'))->open() !!}
        @if (array_get($settings, 'paid-entries'))
            <div class="entries-are-paid">
                <div class="row">
                    <div class="col-xs-12 col-md-4">
                        <h3>{{ trans('payments.titles.payment_provider') }}</h3>
                        <div class="form-group">
                            {!! html()->label(trans('payments.form.payment_gateway.label'), 'payment-gateway') !!}
                            {!! html()->select('setting[payment-gateway]', $gateways, array_get($settings, 'payment-gateway'))->attributes(['class' => 'form-control', 'id' => 'payment-gateway-selector']) !!}
                        </div>
                    </div>
                    <div class="col-xs-12 col-md-4">
                        <h3>{{ trans('payments.titles.gateway_credentials') }}</h3>
                        <div id="authorize-net-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.authorize_net.api_login_id'), 'authorize-net-api-login-id') !!}
                                {!! html()->text('setting[authorize-net-api-login-id]', array_get($settings, 'authorize-net-api-login-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.authorize_net.transaction_key'), 'authorize-net-transaction-key') !!}
                                {!! html()->text('setting[authorize-net-transaction-key]', array_get($settings, 'authorize-net-transaction-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="bluepay-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.bluepay.account_id'), 'bluepay-account-id') !!}
                                {!! html()->text('setting[bluepay-account-id]', array_get($settings, 'bluepay-account-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.bluepay.secret_key'), 'bluepay-secret-key') !!}
                                {!! html()->text('setting[bluepay-secret-key]', array_get($settings, 'bluepay-secret-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="bpoint-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.bpoint.api_username'), 'bpoint-api-username') !!}
                                {!! html()->text('setting[bpoint-api-username]', array_get($settings, 'bpoint-api-username'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.bpoint.api_password'), 'bpoint-api-password') !!}
                                {!! html()->text('setting[bpoint-api-password]', array_get($settings, 'bpoint-api-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.bpoint.merchant_number'), 'bpoint-merchant-number') !!}
                                {!! html()->text('setting[bpoint-merchant-number]', array_get($settings, 'bpoint-merchant-number'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="ccavenue-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.ccavenue.merchant_id'), 'ccavenue-merchant-id') !!}
                                {!! html()->text('setting[ccavenue-merchant-id]', array_get($settings, 'ccavenue-merchant-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.ccavenue.access_code'), 'ccavenue-access-code') !!}
                                {!! html()->text('setting[ccavenue-access-code]', array_get($settings, 'ccavenue-access-code'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.ccavenue.key'), 'ccavenue-key') !!}
                                {!! html()->text('setting[ccavenue-key]', array_get($settings, 'ccavenue-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="cybersource-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.cybersource.merchant_id'), 'cybersource-merchant-id') !!}
                                {!! html()->text('setting[cybersource-merchant-id]', array_get($settings, 'cybersource-merchant-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.cybersource.transaction_key'), 'cybersource-transaction-key') !!}
                                {!! html()->textarea('setting[cybersource-transaction-key]', array_get($settings, 'cybersource-transaction-key'))->attributes(['class' => 'form-control', 'rows' => 3]) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.cybersource.org_unit_id'), 'cybersource-org-unit-id') !!}
                                {!! html()->text('setting[cybersource-org-unit-id]', array_get($settings, 'cybersource-org-unit-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.cybersource.api_identifier'), 'cybersource-api-identifier') !!}
                                {!! html()->text('setting[cybersource-api-identifier]', array_get($settings, 'cybersource-api-identifier'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.cybersource.api_key'), 'cybersource-api-key') !!}
                                {!! html()->text('setting[cybersource-api-key]', array_get($settings, 'cybersource-api-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="eway-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.eway.api_key'), 'eway-api-key') !!}
                                {!! html()->text('setting[eway-api-key]', array_get($settings, 'eway-api-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.eway.api_password'), 'eway-password') !!}
                                {!! html()->text('setting[eway-password]', array_get($settings, 'eway-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="eway-redirect-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.eway_redirect.api_key'), 'eway-redirect-api-key') !!}
                                {!! html()->text('setting[eway-redirect-api-key]', array_get($settings, 'eway-redirect-api-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.eway_redirect.api_password'), 'eway-redirect-password') !!}
                                {!! html()->text('setting[eway-redirect-password]', array_get($settings, 'eway-redirect-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="mercanet-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.mercanet.merchant_id'), 'mercanet-merchant-id') !!}
                                {!! html()->text('setting[mercanet-merchant-id]', array_get($settings, 'mercanet-merchant-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.mercanet.secret_key'), 'mercanet-secret-key') !!}
                                {!! html()->text('setting[mercanet-secret-key]', array_get($settings, 'mercanet-secret-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="nab-transact-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.nab_transact.merchant_id'), 'nab-transact-merchant-id') !!}
                                {!! html()->text('setting[nab-transact-merchant-id]', array_get($settings, 'nab-transact-merchant-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.nab_transact.password'), 'nab-transact-password') !!}
                                {!! html()->text('setting[nab-transact-password]', array_get($settings, 'nab-transact-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="netbanx-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.netbanx.account_number'), 'netbanx-account-number') !!}
                                {!! html()->text('setting[netbanx-account-number]', array_get($settings, 'netbanx-account-number'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.netbanx.store_id'), 'netbanx-store-id') !!}
                                {!! html()->text('setting[netbanx-store-id]', array_get($settings, 'netbanx-store-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.netbanx.store_password'), 'netbanx-store-password') !!}
                                {!! html()->text('setting[netbanx-store-password]', array_get($settings, 'netbanx-store-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="sagepay-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.sagepay.vendor'), 'sagepay-vendor') !!}
                                {!! html()->text('setting[sagepay-vendor]', array_get($settings, 'sagepay-vendor'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.sagepay.referrer_id'), 'sagepay-referrer-id') !!}
                                {!! html()->text('setting[sagepay-referrer-id]', array_get($settings, 'sagepay-referrer-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="securepay-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.securepay.merchant_id'), 'securepay-merchant-id') !!}
                                {!! html()->text('setting[securepay-merchant-id]', array_get($settings, 'securepay-merchant-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.securepay.transaction_password'), 'securepay-transaction-password') !!}
                                {!! html()->text('setting[securepay-transaction-password]', array_get($settings, 'securepay-transaction-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="stripe-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.stripe.api_key'), 'stripe-api-key') !!}
                                {!! html()->text('setting[stripe-api-key]', array_get($settings, 'stripe-api-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="stripe-connect-credentials">
                            <div class="form-group">
                                @if (array_has($settings, 'stripe-connect-user-id'))
                                    @lang('payments.form.authorize_stripe.authorized')
                                    <div>
                                        <a href="{{ route('payment.deauthenticate', ['gateway' => 'stripe']) }}">
                                            @lang('payments.form.authorize_stripe.deauthenticate')
                                        </a>
                                    </div>
                                @else
                                    <div id="stripe-connect-auth-endpoint" class="hidden" data-url="{{ payments_auth_url('stripe') }}"></div>
                                    <button type="button" id="stripe-connect-authorise" class="btn btn-primary btn-lg">
                                        {!! trans('payments.form.authorize_stripe.button') !!}
                                    </button>
                                @endif
                            </div>
                        </div>
                        <div id="paystack-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.paystack.public_key'), 'paystack-public-key') !!}
                                {!! html()->text('setting[paystack-public-key]', array_get($settings, 'paystack-public-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.paystack.secret_key'), 'paystack-secret-key') !!}
                                {!! html()->text('setting[paystack-secret-key]', array_get($settings, 'paystack-secret-key'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.paystack.merchant_email'), 'paystack-merchant-email') !!}
                                {!! html()->text('setting[paystack-merchant-email]', array_get($settings, 'paystack-merchant-email'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="payflow-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.payflow.partner'), 'payflow-partner') !!}
                                {!! html()->text('setting[payflow-partner]', array_get($settings, 'payflow-partner'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.payflow.vendor'), 'payflow-vendor') !!}
                                {!! html()->text('setting[payflow-vendor]', array_get($settings, 'payflow-vendor'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.payflow.username'), 'payflow-username') !!}
                                {!! html()->text('setting[payflow-username]', array_get($settings, 'payflow-username'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.payflow.password'), 'payflow-password') !!}
                                {!! html()->text('setting[payflow-password]', array_get($settings, 'payflow-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="realex-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.realex.merchant_id'), 'realex-merchant-id') !!}
                                {!! html()->text('setting[realex-merchant-id]', array_get($settings, 'realex-merchant-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.realex.account'), 'realex-account') !!}
                                {!! html()->text('setting[realex-account]', array_get($settings, 'realex-account'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.realex.shared_secret'), 'realex-shared-secret') !!}
                                {!! html()->text('setting[realex-shared-secret]', array_get($settings, 'realex-shared-secret'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="westpac-payway-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.westpac_payway.username'), 'westpac-payway-username') !!}
                                {!! html()->text('setting[westpac-payway-username]', array_get($settings, 'westpac-payway-username'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.westpac_payway.password'), 'westpac-payway-password') !!}
                                {!! html()->text('setting[westpac-payway-password]', array_get($settings, 'westpac-payway-password'))->attributes(['class' => 'form-control']) !!}
                            </div>
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.westpac_payway.merchant_id'), 'westpac-payway-merchant-id') !!}
                                {!! html()->text('setting[westpac-payway-merchant-id]', array_get($settings, 'westpac-payway-merchant-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                        <div id="worldpay-credentials">
                            <div class="form-group">
                                {!! html()->label(trans('payments.form.worldpay.installation_id'), 'worldpay-installation-id') !!}
                                {!! html()->text('setting[worldpay-installation-id]', array_get($settings, 'worldpay-installation-id'))->attributes(['class' => 'form-control']) !!}
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-12 col-md-4">
                        <h3>{{ trans('payments.titles.accepted_cards') }}</h3>
                        <p>{{ trans('payments.info.accepted_cards') }}</p>

                        @foreach(Config::get('payments.available_cards') as $key => $value)
                            <div class="form-group">
                                <div class="checkbox styled">
                                    {!! html()->hidden("setting[accept-{$value}]", 0) !!}
                                    {!! html()->checkbox("setting[accept-{$value}]", array_get($settings, "accept-{$value}"), 1)->attributes(['id' => "accept-{$value}"]) !!}
                                    <label for="accept-{{$value}}">
                                        {{ trans('payments.form.accept_'.$value.'.label') }}
                                    </label>
                                </div>
                            </div>
                        @endforeach

                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-xs-12">
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-lg uploading-disable">
                            {!! trans('buttons.save') !!}
                        </button>
                    </div>
                </div>
            </div>
        @else
            @lang('payments.disabled', ['url' => route('payment.general')])
        @endif
        {!! html()->form()->close() !!}
    </div>
@stop
