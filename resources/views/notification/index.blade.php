<?php $searching = query_parameters(['trigger']); ?>

@section('title')
    {!! HTML::pageTitle(trans('notifications.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <notification-list id="notification-list" :ids="@js($notificationIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('notifications.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @if($canCreate)
                        <div class="selector-buttons">
                            {!! Button::link(route('notification.new'), trans('notifications.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                        </div>
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.copy', ['resource' => 'notification'])</li>
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'notification'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'notification'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>

                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $notifications])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($notifications->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $notifications->items(), 'class' => 'assignments-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $notifications])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('notifications.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </notification-list>
@stop
