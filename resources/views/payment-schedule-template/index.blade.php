<?php $searching = query_parameters(['manager', 'status']); ?>

@section('title')
    {!! HTML::pageTitle(trans('payment-schedule-templates.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <payment-schedule-templates-list id="payment-schedule-templates-list" :ids="@js($paymentScheduleTemplatesIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('payment-schedule-templates.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @if (Consumer::isProgramManager())
                        <div class="selector-buttons">
                            <div class="selector-buttons buttons-spacer island">
                                {!! Button::link(route('payment-schedule-template.new'), trans('payment-schedule-templates.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                            </div>
                        </div>
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => true, 'disableAdvanced' => true])
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                area="{{ $area }}"
                title="{{ trans('search.shortcuts-bar.title') }}"
                :saved-views="@js($savedViews)"
                id="saved-views-shortcuts-bar"
                class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.delete', ['resource' => 'payment-schedule-template'])</li>
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $paymentScheduleTemplates])
                    </div>
                </div>
            </div>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <!-- Result set -->
            <div>
                @if ($paymentScheduleTemplates->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $paymentScheduleTemplates->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $paymentScheduleTemplates])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('payment-schedule-templates.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </payment-schedule-templates-list>
@stop
