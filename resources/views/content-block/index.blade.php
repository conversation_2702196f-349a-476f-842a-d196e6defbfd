<?php $searching = query_parameters(['location']); ?>

@section('title')
    {!! HTML::pageTitle(trans('content-block.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <content-block-list id="content-block-list" :ids="@js($contentBlockIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('content-block.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                      {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    <div class="selector-buttons">
                        {!! Button::link(route('content-block.new'), trans('content-block.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction(['content.export']) !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.delete', ['resource' => 'content-block'])</li>
                        </ul>
                    </list-action-dropdown>

                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $contentBlocks])
                    </div>
                </div>
            </div>
        @include('partials.errors.display')
        @include('partials.errors.message')

        <!-- Result set -->
            <div class="mtm">
                @if ($contentBlocks->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $contentBlocks->items(), 'class' => 'assignments-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $contentBlocks])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('content-block.tables.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </content-block-list>
@stop
