@section('title')
    {!! HTML::pageTitle(trans('broadcasts.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <broadcast-list id="broadcast-list" :ids="@js($broadcastIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('broadcasts.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                    </div>
                </div>
            </div>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-12">
                    <div class="search-info">
                        @include('partials.page.pagination-info', ['paginator' => $broadcasts])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($broadcasts->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $broadcasts->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $broadcasts])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('broadcasts.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </broadcast-list>
@stop
