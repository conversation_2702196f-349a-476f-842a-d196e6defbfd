@section('title')
    {!! HTML::pageTitle(trans('tags.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <tag-list id="tag-list" :ids="@js($tagsIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('tags.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                    </div>
                    <div class="selector-buttons">
                        {!! Button::link(route('tag.new'), trans('tags.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => false, 'disableAdvanced' => true])
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.delete', ['resource' => 'tag'])</li>
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $tags])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($tags->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $tags->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $tags])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('tags.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </tag-list>
@stop
