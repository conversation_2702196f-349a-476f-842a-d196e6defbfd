@section('title')
    {!! HTML::pageTitle(trans('awards.title.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <award-list id="award-list" :ids="@js($awardsIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('awards.title.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    <div class="selector-buttons">
                        {!! Button::link(route('award.new'), trans('awards.title.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'disableAdvanced' => true])
                @endcomponent

            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.copy', ['resource' => 'award'])</li>
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'award'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'award'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $awards])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($awards->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $awards->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $awards])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('awards.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </award-list>
@stop
