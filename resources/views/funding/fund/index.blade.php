<?php $searching = query_parameters([]); ?>

@section('title')
    {!! HTML::pageTitle(trans('funding.titles.fund.list')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <funds-allocations-list id="funds-list" :ids="@js($fundIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('funding.titles.fund.list') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    <div class="selector-buttons">
                        @if (Consumer::can('create', 'Funding'))
                            {!! Button::link(route('funding.fund.new'), trans('funding.titles.fund.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                        @endif
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => $searching, 'disableAdvanced' => true]))
                    @slot('actions')
                    {!! HTML::exportAction('funds.export') !!}
                    @endslot
                @endcomponent
            </div>

            <!-- Message -->
            @include('partials.errors.display')
            @include('partials.errors.message')


            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (Consumer::can('delete', 'Funding'))
                                <li>@include('partials.list-actions.delete', ['resource' => 'funding.fund'])</li>
                            @endif
                            @if (Consumer::can('create', 'Funding') && !trashed_filter_active() && !archived_filter_active())
                                <li>@include('partials.list-actions.copy', ['resource' => 'funding.fund'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $funds])
                    </div>
                </div>
            </div>

            <div>
                @if ($funds->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $funds->items(), 'class' => 'table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $funds])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('funding.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </funds-allocations-list>
@stop
