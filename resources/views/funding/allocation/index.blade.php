<?php $searching = query_parameters(['category', 'chapter', 'fund', 'tag']); ?>

@section('title')
    {!! HTML::pageTitle(trans('funding.titles.allocation.list')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <funds-allocations-list id="allocations-list" :ids="@js($allocationIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('funding.titles.allocation.list') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('allocations.export') !!}
                        {!! HTML::broadcast('broadcast.new', 'allocations') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                area="{{ $area }}"
                title="{{ trans('search.shortcuts-bar.title') }}"
                :saved-views="@js($savedViews)"
                id="saved-views-shortcuts-bar"
                class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            <!-- Message -->
            @include('partials.errors.display')
            @include('partials.errors.message')

            <portal-target name="create-document" v-if="createDocumentRevealed" multiple></portal-target>
            <portal-target name="tagger" v-if="taggerRevealed"></portal-target>
            <portal-target name="untagger" v-if="untaggerRevealed"></portal-target>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (!trashed_filter_active() && feature_enabled('documents') && Consumer::can('create', 'Documents'))
                                <li>@include('partials.list-actions.create-document', ['resource' => 'allocation'])</li>
                            @endif

                            <li>@include('partials.list-actions.delete', ['resource' => 'funding.allocation'])</li>

                            @if (Consumer::can('create', 'Tags'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.tag', ['resource' => 'funding.allocation', 'labels' => ['button' => trans('buttons.tag')]])</li>
                                <li>@include('partials.list-actions.untag', ['resource' => 'funding.allocation', 'labels' => ['button' => trans('buttons.remove_tag')]])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>


                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $allocations])
                    </div>
                </div>
            </div>

            <div>
                @if ($allocations->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $allocations->items(), 'class' => 'table markers-table'])
                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $allocations])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('funding.table.no-allocations')</p>
                    </div>
                @endif
            </div>
        </div>
    </funds-allocations-list>

    @include('allocation-payment.partials.modal')
@stop
