<?php $searching = query_parameters(['tag', 'chapter', 'consensus', 'state']); ?>

@section('title')
    {!! HTML::pageTitle(trans('judging.titles.leaderboard')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    @if (empty($scoreSets) || $scoreSets->isEmpty())
        <leaderboard id="leaderboard" :ids="[]" inline-template>
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('judging.titles.leaderboard') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        @include('partials.page.selectors.state', ['archived' => true])
                        <h2 class="h3">@lang('judging.table.no-scoresets')</h2>
                    </div>
                </div>
            </div>
        </leaderboard>
    @else
        <leaderboard id="leaderboard" :ids="@js($entryIds)" inline-template>
            <div id="searchResults">
                <div class="selectors island">
                    <div class="selector-title">
                        <div class="mrx">
                            <h1>{!! trans('judging.titles.leaderboard') !!}</h1>
                            @include('partials.holocron.feature-intro-revealer')
                        </div>
                        <div class="inline-block mtm -mlm">
                            @include('partials.page.selectors.season')
                            @include('partials.page.selectors.state', ['archived' => true])
                        </div>
                    </div>

                    @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                        @slot('actions')
                            {!! HTML::exportAction($exports, $legacyExports) !!}
                            {!! HTML::broadcast('broadcast.new', $scoreSet->mode . '-leaderboard') !!}
                        @endslot
                    @endcomponent
                </div>

                <saved-views-shortcuts-bar
                    area="{{ $area }}"
                    title="{{ trans('search.shortcuts-bar.title') }}"
                    :saved-views="@js($savedViews)"
                    id="saved-views-shortcuts-bar"
                    class="shortcuts-bar-space-above"
                ></saved-views-shortcuts-bar>

                <h2 class="h3 mts">
                    <div class="form-inline">
                        <div class="form-group">
                            <label for="scoreSetSelector">@lang('judging.titles.score_set')</label>&nbsp;
                            <score-set-selector
                                id="scoreSetSelector"
                                url="{{ current_url(['score-set' => null, 'page' => null]) }}"
                                :score-sets="@js($scoreSets)"
                                :current-score-set="{{ object_get($scoreSet, 'id') }}">
                            </score-set-selector>
                        </div>
                    </div>
                </h2>

                @if ($scoreSet)
                    @if (Consumer::isManager() && Request::get('archived') !== 'only' && trans()->has('judging.recalculate.warnings.'.$scoreSet->mode))
                        <div class="alert-warning sticky island" role="alert">
                            <div class="icon">
                                <i class="af-icons-md af-icons-md-alert-warning"></i>
                            </div>
                            <div class="message">
                                @lang('judging.recalculate.warnings.'.$scoreSet->mode)
                            </div>
                        </div>
                    @endif
                    @if ($scoreSet->recalculateNeeded)
                        <div class="alert-warning sticky island" role="alert">
                            <div class="icon">
                                <i class="af-icons-md af-icons-md-alert-warning"></i>
                            </div>
                            <div class="message">
                                @lang('judging.recalculate.recalculate-needed')
                            </div>
                        </div>
                    @endif
                    @if ($selectorCategories->count() > 1)
                        <div class="quick-selector" id="quick-selector-category" v-pre>
                            @include('partials.page.quick-selector', ['field' => 'category', 'options' => $selectorCategories])
                        </div>
                    @endif

                    @include('partials.errors.display')
                    @include('partials.errors.message')

                    <div
                        class="leaderboard-recalculate {{ ($recalculating ? '' : 'hidden ').$recalculateStatus->getRefreshToken() }}">
                        @include('partials.errors.message', ['message' => trans('judging.recalculate.processing-warning'), 'type' => 'warning'])
                        <div class="row island">
                            <div class="col-xs-12">
                                <i class="af-icons af-icons-repeat af-icons-animate-rotate"></i>&nbsp;@lang('judging.recalculate.processing.'.$scoreSet->mode)
                            </div>
                        </div>
                    </div>
                    <div
                        class="leaderboard-sync {{ (($recalculating || $syncStatus->hasFinishedProcessing()) ? 'hidden ' : '').$syncStatus->getRefreshToken() }}">
                        @include('partials.errors.message', ['message' => trans('judging.recalculate.processing-warning'), 'type' => 'warning'])
                        <div class="row island">
                            <div class="col-xs-12">
                                <i class="af-icons af-icons-repeat af-icons-animate-rotate"></i>&nbsp;@lang('judging.recalculate.sync')
                            </div>
                        </div>
                    </div>

                    <portal-target name="tagger" v-if="taggerRevealed"></portal-target>
                    <portal-target name="untagger" v-if="untaggerRevealed"></portal-target>
                    <portal-target name="fund-allocator" v-if="fundAllocatorRevealed" multiple></portal-target>
                    <portal-target name="create-document" v-if="createDocumentRevealed" multiple></portal-target>
                    <portal-target name="grant-status-selector" v-if="grantStatusSelectorRevealed" multiple></portal-target>

                    <div class="row mtm">
                        <div class="col-xs-12 col-lg-6">
                            <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}"
                                                  v-cloak>
                                <ul class="action-list">
                                    @if (Consumer::can('delete', 'EntriesAll'))
                                        <li>
                                            @include('partials.list-actions.archiver', ['resource' => 'entry.manager', 'params' => ['redirect' => route('leaderboard.index')]])
                                        </li>
                                        <li>
                                            @include('partials.list-actions.unarchiver', ['resource' => 'entry.manager', 'params' => ['redirect' => route('leaderboard.index')]])
                                        </li>
                                    @endif
                                    <li>
                                        @include('partials.list-actions.download', ['resource' => 'leaderboard', 'params' => ['scoreSet' => $scoreSet->slug]])
                                    </li>
                                    @if (Consumer::can('create', 'Tags'))
                                        <li>
                                            @include('partials.list-actions.tag', ['resource' => 'entry.manager', 'params' => ['redirect' => route('leaderboard.index')]])
                                        </li>
                                        <li>
                                            @include('partials.list-actions.untag', ['resource' => 'entry.manager', 'params' => ['redirect' => route('leaderboard.index')]])
                                        </li>
                                    @endif
                                    @if (feature_enabled('fund_management'))
                                        <li>
                                            @include('partials.list-actions.fund-allocations')
                                        </li>
                                    @endif

                                    @if(feature_enabled('grants') && Consumer::can('create', 'Grants'))
                                        <li>@include('partials.list-actions.grant-status-selector')</li>
                                    @endif

                                    @if (feature_enabled('documents') && Consumer::can('create', 'Documents'))
                                        <li>@include('partials.list-actions.create-document', ['resource' => 'entry'])</li>
                                    @endif
                                </ul>
                            </list-action-dropdown>

                        </div>
                        <div class="col-xs-12 col-lg-6">
                            <div class="search-info" v-pre>
                                @include('partials.page.active-filters', ['filters' => request()->all(), 'resource' => 'assignments', 'hide' => ['score-set']])
                                @include('partials.page.pagination-info', ['paginator' => $entries])
                            </div>
                        </div>
                    </div>
                @endif

                <div>
                    @if ($entries->count())
                        @include('search.datatable', ['columnator' => $columnator, 'results' => $entries->items(), 'class' => 'table markers-table'])

                        <div class="row">
                            <div class="col-xs-12">
                                @include('partials.page.pagination', ['paginator' => $entries])
                            </div>
                        </div>
                    @else
                        <div>
                            <p>@lang('judging.table.empty')</p>
                        </div>
                    @endif
                </div>
            </div>
        </leaderboard>

        @if (Consumer::isManager() && Request::get('archived') !== 'only' && $scoreSet && $scoreSet->canRecalculate())
            <div class="row island">
                <div class="col-xs-12">
                    {!! html()->form(action:route('leaderboard.recalculate', $scoreSet))->open() !!}
                    {!! html()->submit(trans('judging.recalculate.'.($recalculating ? 'processing' : 'start').'.'.$scoreSet->mode))->class('btn btn-tertiary'.($recalculating ? ' disabled' : ''))->disabled($recalculating) !!}
                    <help-icon content="{{ trans('judging.recalculate.helptext') }}"></help-icon>
                    {{ Cache::has($key = 'recalculate.last.'.$scoreSet->id) ? trans('judging.recalculate.last', ['diff' => Cache::get($key)->diffForHumans()]) : '' }}
                    {!! html()->form()->close() !!}
                </div>
            </div>
        @endif
    @endif
@stop
