<?php $searching = query_parameters(['panel']); ?>

@section('title')
    {!! HTML::pageTitle(trans('judging.titles.progress')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    @if ($scoreSets->isEmpty())
        <div class="row row-relative island">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('judging.titles.progress') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                    </div>
                    <h2 class="h3">@lang('judging.table.no-scoresets')</h2>
                </div>
            </div>
        </div>
    @else
        <leaderboard-progress id="leaderboard-progress" :ids="@js($judgeIds)" inline-template>
            <div id="searchResults">
                <div class="selectors island">
                    <div class="selector-title">
                        <div class="mrx">
                            <h1>{{ trans('judging.titles.progress') }}</h1>
                            @include('partials.holocron.feature-intro-revealer')
                        </div>
                        <div class="inline-block mtm -mlm">
                            @include('partials.page.selectors.season')
                        </div>
                    </div>
                    @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                        @slot('actions')
                            {!! HTML::exportAction($export) !!}
                            {!! HTML::broadcast('broadcast.new', $scoreSet->mode . '-progress') !!}
                        @endslot
                    @endcomponent
                </div>

                <saved-views-shortcuts-bar
                      area="{{ $area }}"
                      title="{{ trans('search.shortcuts-bar.title') }}"
                      :saved-views="@js($savedViews)"
                      id="saved-views-shortcuts-bar"
                      class="shortcuts-bar-space-above"
                ></saved-views-shortcuts-bar>

                <h2 class="h3 mts">
                    <div class="form-inline">
                        <div class="form-group">
                            @if ($scoreSets->count())
                                <label for="scoreSetSelector">@lang('judging.titles.score_set')</label>&nbsp;
                                <score-set-selector
                                    id="scoreSetSelector"
                                    url="{{ current_url(['score-set' => null, 'page' => null]) }}"
                                    :score-sets="@js($scoreSets)"
                                    :current-score-set="{{ object_get($scoreSet, 'id') }}">
                                </score-set-selector>
                            @else
                                @lang('judging.table.no-scoresets')
                            @endif
                        </div>
                    </div>
                </h2>

                @include('partials.errors.display')
                @include('partials.errors.message')

                <div class="row mtm">
                    <div class="col-xs-12">
                        <div class="search-info">
                            @include('partials.page.active-filters', ['filters' => request()->all()])
                            @include('partials.page.pagination-info', ['paginator' => $judges])
                        </div>
                    </div>
                </div>

                <div>
                    @if ($judges->count())
                        @include('search.datatable', ['columnator' => $columnator, 'results' => $judges->items(), 'class' => 'table markers-table'])
                        <div class="row">
                            <div class="col-xs-12">
                                @include('partials.page.pagination', ['paginator' => $judges])
                            </div>
                        </div>
                    @else
                        <div>
                            <p>@lang('judging.table.empty')</p>
                        </div>
                    @endif
                </div>
            </div>
        </leaderboard-progress>
    @endif
@stop
