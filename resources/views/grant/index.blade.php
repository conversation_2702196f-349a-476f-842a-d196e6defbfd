<?php
$searching = query_parameters(['chapter', 'category', 'entrant', 'tag', 'grant_status']);
$listActionParams = ['redirect' => route('grant.manager.index')]
?>
@section('title')
    {!! HTML::pageTitle($title = trans('grants.titles.manage')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    <grant-list id="grant-list" :ids="@js($grantIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ $title }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        {!! html()->trashedWithArchivedSelect('partials.page.selectors.state') !!}
                    </div>

                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction(['manage_grants.export', 'contributors.export', 'files.export']) !!}
                        {!! HTML::broadcast('broadcast.new', 'entrants-with-grants') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                area="{{ $area }}"
                title="{{ trans('search.shortcuts-bar.title') }}"
                :saved-views="@js($savedViews)"
                id="saved-views-shortcuts-bar"
                class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <portal-target name="tagger" v-if="taggerRevealed"></portal-target>
            <portal-target name="untagger" v-if="untaggerRevealed"></portal-target>
            <portal-target name="reviewer" v-if="reviewerRevealed" multiple></portal-target>
            <portal-target name="duplicate-archiver" v-if="duplicateArchiverRevealed"></portal-target>
            <portal-target name="contract-creator" v-if="contractCreatorRevealed" multiple></portal-target>
            <portal-target name="create-document" v-if="createDocumentRevealed" multiple></portal-target>
            <portal-target name="fund-allocator" v-if="fundAllocatorRevealed" multiple></portal-target>
            <portal-target name="grant-status-selector" v-if="grantStatusSelectorRevealed" multiple></portal-target>
            <portal-target name="schedule-grant-report" v-if="scheduleGrantReportRevealed" multiple></portal-target>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (Consumer::can('create', 'Grants') && !trashed_filter_active() && !archived_filter_active())
                                <li>@include('partials.list-actions.copy', ['resource' => 'entry.manager'])</li>
                            @endif
                            @if (Consumer::can('delete', 'EntriesAll'))
                                <li>@include('partials.list-actions.delete', ['resource' => 'entry.manager', 'params' => $listActionParams])</li>
                                <li>@include('partials.list-actions.archive', ['resource' => 'entry.manager', 'params' => $listActionParams])</li>
                            @endif

                            <li>@include('partials.list-actions.download', ['resource' => 'grant.manager'])</li>

                            @if (Consumer::can('create', 'Tags'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.tag', ['resource' => 'entry.manager', 'params' => $listActionParams, 'labels' => ['button' => trans('buttons.tag')]])</li>
                                <li>@include('partials.list-actions.untag', ['resource' => 'entry.manager', 'params' => $listActionParams, 'labels' => ['button' => trans('buttons.remove_tag')]])</li>
                            @endif

                            @if (selected_season_is_active())
                                @if (feature_enabled('review_flow'))
                                    <li class="divider"></li>
                                    <li>@include('partials.list-actions.review', ['resource' => 'entry.manager', 'params' => $listActionParams, 'labels' => ['button' => trans('buttons.initiate_review_stage')]])</li>
                                @else
                                    <li class="divider"></li>
                                    <li>
                                        <a href="{{ route('feature.disabled', ['review_flow']) }}">
                                            @lang('buttons.initiate_review_stage')
                                        </a>
                                    </li>
                                @endif
                            @endif

                            @if (!trashed_filter_active() && feature_enabled('contracts'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.add-contract', ['resource' => 'entry.manager', 'params' => $listActionParams, 'labels' => ['button' => trans('entries.contracts.add')]])</li>
                            @endif

                            @if (!trashed_filter_active() && feature_enabled('documents') && Consumer::can('create', 'Documents'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.create-document', ['resource' => 'entry'])</li>
                            @endif

                            @if(feature_enabled('fund_management'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.fund-allocations')</li>
                            @endif

                            @if(feature_enabled('grants') && Consumer::can('create', 'Grants'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.grant-status-selector')</li>
                            @endif

                            @if(feature_enabled('grant_reports') && Consumer::can('create', 'Grants'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.schedule-report', ['showReportSelector' => true])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info" v-pre>
                        @include('partials.page.active-filters', ['filters' => request()->all(), 'resource' => 'assignments', 'hide' => ['score-set']])
                        @include('partials.page.pagination-info', ['paginator' => $grants])
                    </div>
                </div>
            </div>
            <div>
                @if ($grants->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $grants->items(), 'class' => 'table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $grants])
                        </div>
                    </div>
                @else
                    <div>
                        <p>{{ trans('shared.table.empty', ['resource' => Term::plural('grant')]) }}</p>
                    </div>
                @endif
            </div>

            <collaborators-list-modal></collaborators-list-modal>
            <collaborators-invite-modal></collaborators-invite-modal>
        </div>
    </grant-list>
@stop
