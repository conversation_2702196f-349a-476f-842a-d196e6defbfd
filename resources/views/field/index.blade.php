<?php $searching = query_parameters(['category', 'conditional', 'resource', 'type', 'tab', 'protection']); ?>

@section('title')
    {!! HTML::pageTitle(trans('fields.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <field-list id="field-list" :ids="@js($fieldsIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('fields.titles.main') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter(['entry', 'report']) !!}
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>

                    @if (Consumer::can('create', 'Fields'))
                        @include('partials.list-actions.add-resource', ['label' => sentence_case(trans('fields.titles.new')), 'route' => route('field.resource'), 'extraOptions' => $extraOptions, 'allowedTypes' => ['entry', 'report']])
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('fields.export') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.copy', ['resource' => 'field'])</li>
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'field'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'field'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $fields])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($fields->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $fields->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $fields])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('fields.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </field-list>
@stop
