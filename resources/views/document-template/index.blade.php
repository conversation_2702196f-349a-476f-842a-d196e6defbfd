<?php $searching = query_parameters([]); ?>

@section('title')
    {!! HTML::pageTitle(trans('document-templates.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <document-templates-list id="document-templates-list" :ids="@js($documentTemplateIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('document-templates.titles.main') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                       {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>

                    @if (Consumer::can('create', 'DocumentTemplates'))
                        <div class="selector-buttons">
                            {!! Button::link(route('document-template.new'), trans('document-templates.titles.new'), ['type' => 'primary', 'size' => 'lg']) !!}
                        </div>
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => $searching, 'disableAdvanced' => true])
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                area="{{ $area }}"
                title="{{ trans('search.shortcuts-bar.title') }}"
                :saved-views="@js($savedViews)"
                id="saved-views-shortcuts-bar"
                class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    @include('document-template.partials.actions')
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $documentTemplates])
                    </div>
                </div>
            </div>

            <div>
                @if ($documentTemplates->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $documentTemplates->items(), 'class' => 'table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $documentTemplates])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('document-templates.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </document-templates-list>
@stop
