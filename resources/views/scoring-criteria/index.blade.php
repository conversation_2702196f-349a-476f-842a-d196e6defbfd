<?php $searching = query_parameters(['category', 'field', 'score-set']); ?>

@section('title')
    {!! HTML::pageTitle(trans('scoring-criteria.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <scoring-criteria-list id="scoring-criteria-list" :ids="@js($criteriaIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('scoring-criteria.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        @include('partials.page.selectors.state', ['archived' => true, 'deleted' => true])
                    </div>

                    @if (Consumer::can('create', 'ScoringCriteria'))
                        @include('partials.list-actions.add-resource', ['label' => trans('scoring-criteria.titles.new'), 'route' => route('scoring-criteria.new')])
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('scoring_criteria.export') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (!trashed_filter_active())
                                <li>@include('partials.list-actions.copy', ['resource' => 'scoring-criteria'])</li>
                            @endif
                            <li>@include('partials.list-actions.delete', ['resource' => 'scoring-criteria'])</li>
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $criteria])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($criteria->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $criteria->items(), 'class' => 'content-blocks-table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $criteria])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('scoring-criteria.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </scoring-criteria-list>
@stop
