<?php $searching = query_parameters(['mode', 'agreement', 'registration_form', 'field']); ?>

@section('title')
    {!! HTML::pageTitle(trans('score-set.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <score-sets-list id="score-sets-list" :ids="@js($scoreSetIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('score-set.titles.main') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        @include('partials.page.selectors.state', ['archived' => true, 'deleted' => true])
                    </div>

                    @if (Consumer::can('create', 'ScoreSets'))
                        @include('partials.list-actions.add-resource', ['label' => trans('score-set.titles.new'), 'route' => route('score-set.new')])
                    @endif
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('score_sets.export') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @includewhen(isTrialAccount(), 'partials.trial.info', ['trialMessage' => trans('score-set.disabled_public')])

            <!-- Message -->
            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    @include('score-set.partials.actions')
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $scoreSets])
                    </div>
                </div>
            </div>

            <div>
                @if ($scoreSets->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $scoreSets->items(), 'class' => 'table markers-table score-sets-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $scoreSets])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('score-set.table.empty')</p>
                    </div>
                @endif
            </div>

        </div>
    </score-sets-list>
@stop

