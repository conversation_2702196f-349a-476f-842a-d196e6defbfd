<?php $searching = query_parameters(['judging-mode', 'score-set']); ?>

@section('title')
    {!! HTML::pageTitle(trans('panels.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <panel-list id="panel-list" :ids="@js($panelIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                    <div class="selector-title">
                        <div class="mrx">
                            <h1>{!! trans('panels.titles.main') !!}</h1>
                            @include('partials.holocron.feature-intro-revealer')
                        </div>
                        <div class="inline-block mtm -mlm">
                            @include('partials.page.selectors.season')
                            {!! html()->formFilter() !!}
                            @include('partials.page.selectors.state', ['archived' => true, 'deleted' => true])
                        </div>
                        @includeWhen($canCreate, 'partials.list-actions.add-resource', ['label' => trans('panels.titles.new'), 'route' => route('panel.new')])
                    </div>
                    @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                        @slot('actions')
                        {!! HTML::exportAction('panels.export') !!}
                        @endslot
                    @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'panel'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'panel'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $panels])
                    </div>
                </div>
            </div>

            <div>
                @if ($panels->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $panels->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $panels])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('panels.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </panel-list>
@stop
