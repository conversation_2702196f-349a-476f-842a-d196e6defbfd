<?php $searching = query_parameters(['chapter', 'category', 'entrant', 'status', 'moderation', 'tag', 'review_status', 'price', 'payment_status', 'plagiarism_scan_status']); ?>

@section('title')
    {!! HTML::pageTitle(trans('entries.titles.manager')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    @include('users.confirm')

    <entries-list id="entries-list" :ids="@js($entryIds)" :translations="@js($translations)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('entries.titles.manager') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        @if(feature_enabled('grants') && \Consumer::can('view', 'Grants'))
                            @include('partials.page.selectors.state-and-grants')
                        @else
                            {!! html()->stateSelect('partials.page.selectors.state') !!}
                        @endif
                    </div>
                </div>

                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area', 'tooltipText'))
                    @slot('actions')
                        {{ HTML::exportAction(['manage_entries.export', 'contributors.export', 'files.export']) }}
                        {!! HTML::broadcast('broadcast.new', 'entrants-with-entries') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                area="{{ $area }}"
                title="{{ trans('search.shortcuts-bar.title') }}"
                :saved-views="@js($savedViews)"
                id="saved-views-shortcuts-bar"
                class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            @include('entry.common.copying-entries')

            <portal-target name="tagger" v-if="taggerRevealed"></portal-target>
            <portal-target name="untagger" v-if="untaggerRevealed"></portal-target>
            <portal-target name="reviewer" v-if="reviewerRevealed" multiple></portal-target>
            <portal-target name="duplicate-archiver" v-if="duplicateArchiverRevealed" multiple></portal-target>
            <portal-target name="assign-judges" v-if="assignJudgesRevealed" multiple></portal-target>
            <portal-target name="contract-creator" v-if="contractCreatorRevealed" multiple></portal-target>
            <portal-target name="create-document" v-if="createDocumentRevealed" multiple></portal-target>
            <portal-target name="fund-allocator" v-if="fundAllocatorRevealed" multiple></portal-target>
            <portal-target name="grant-status-selector" v-if="grantStatusSelectorRevealed" multiple></portal-target>
            <portal-target name="schedule-grant-report" v-if="scheduleGrantReportRevealed" multiple></portal-target>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown dropdown-id="entry-manager-actions" revealed-action="reveal"
                                          label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (Consumer::can('create', 'EntriesAll') && !trashed_filter_active() && !archived_filter_active())
                                <li>@include('partials.list-actions.copy', ['resource' => 'entry.manager'])</li>
                            @endif

                            @if (Consumer::can('delete', 'EntriesAll'))
                                <li>@include('partials.list-actions.delete', ['resource' => 'entry.manager'])</li>
                                @if (!trashed_filter_active())
                                    <li>{!! HTML::archive('entry.manager') !!}</li>
                                @endif
                            @endif

                            <li>@include('partials.list-actions.download', ['resource' => 'entry.manager'])</li>

                            @if ($canModerate)
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.moderate', ['resource' => 'entry.manager', 'moderation_status' => 'approved', 'label' => 'approve'])</li>
                                <li>@include('partials.list-actions.moderate', ['resource' => 'entry.manager', 'moderation_status' => 'undecided', 'label' => 'undecide'])</li>
                                <li>@include('partials.list-actions.moderate', ['resource' => 'entry.manager', 'moderation_status' => 'rejected', 'label' => 'reject'])</li>
                            @endif

                            @if (Consumer::can('create', 'Tags'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.tag', ['resource' => 'entry.manager', 'labels' => ['button' => trans('buttons.tag')]])</li>
                                <li>@include('partials.list-actions.untag', ['resource' => 'entry.manager', 'labels' => ['button' => trans('buttons.remove_tag')]])</li>
                            @endif

                            @if (selected_season_is_active() && Consumer::can('create', 'EntriesAll'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.resubmission-required', ['resource' => 'entry.manager'])</li>

                                @if (feature_enabled('review_flow'))
                                    <li>@include('partials.list-actions.review', ['resource' => 'entry.manager', 'labels' => ['button' => trans_elliptic('buttons.initiate_review_stage')]])</li>
                                @else
                                    <li>
                                        <a href="{{ route('feature.disabled', ['review_flow']) }}" class="dropdown-menu-item">
                                            {{ trans_elliptic('buttons.initiate_review_stage') }}
                                        </a>
                                    </li>
                                @endif
                            @endif

                            @if (!trashed_filter_active() && Consumer::can('create', 'EntriesAll'))
                                @if (feature_enabled("manage_duplicates"))
                                    <li>@include('partials.list-actions.duplicate-archive', ['resource' => 'entry.duplicates.duplicate.archive'])</li>
                                @else
                                    <li>
                                        <a href="{{ route('feature.disabled', ['manage_duplicates']) }}" class="dropdown-menu-item">
                                            @lang('entries.duplicates.buttons.set_and_archive')
                                        </a>
                                    </li>
                                @endif

                                <li>@include('partials.list-actions.assign-judges', ['resource' => 'assignment.assign'])</li>
                            @endif

                            @if (!trashed_filter_active() && feature_enabled('contracts') && Consumer::can('create', 'EntriesAll'))
                                <li>@include('partials.list-actions.add-contract', ['resource' => 'entry.manager', 'labels' => ['button' => trans('entries.contracts.add')]])</li>
                            @endif

                            @if (!trashed_filter_active() && feature_enabled('documents') && Consumer::can('create', 'Documents'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.create-document', ['resource' => 'entry'])</li>
                            @endif

                            @if(feature_enabled('fund_management') && Consumer::can('create', 'Funding'))
                                <li>@include('partials.list-actions.fund-allocations')</li>
                            @endif

                             @if(feature_enabled('grants') && Consumer::can('create', 'Grants'))
                                 <li>@include('partials.list-actions.grant-status-selector')</li>
                             @endif

                            @if($canScheduleGrantReports)
                                <li>@include('partials.list-actions.schedule-report', ['showReportSelector' => true])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                    @if (Consumer::can('create', 'EntriesAll') && !trashed_filter_active() && !archived_filter_active())
                        <div class="dropdown action-dropdown">
                            @include('partials.list-actions.edit-form')
                            @include('partials.list-actions.add-resource-inline', ['label' => trans('entries.titles.start'), 'route' => route('entry.manager.start')])
                        </div>
                    @endif
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => request()->all()])
                        @include('partials.page.pagination-info', ['paginator' => $entries])
                    </div>
                </div>
            </div>

            <div>
                @if ($entries->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $entries->items(), 'class' => 'entries-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $entries])
                        </div>
                    </div>
                @else
                    <div>
                        <p>{!! trans('entries.table.empty') !!}</p>
                    </div>
                @endif
            </div>
            <collaborators-list-modal></collaborators-list-modal>
            <collaborators-invite-modal></collaborators-invite-modal>
            <portal-target name="form-inviter" v-if="formInviterRevealed" multiple></portal-target>
        </div>
    </entries-list>

    @if (!trashed_filter_active() && !archived_filter_active())
        {{ link_to_route('entry.duplicates.manage', trans('entries.duplicates.title'), [], ['class' => 'btn btn-tertiary']) }}
    @endif
@stop
