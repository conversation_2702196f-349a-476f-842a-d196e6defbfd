@if ($file->transcodingStatus === 'completed' || $file->transcodingStatus === 'warning')
    @if(isset($embeddedContent) && $embeddedContent)
        <div class="card">
            <div class="preview-container">
                <div class="preview">
                    <video-player
                        file-id="video-{{ $file->id }}"
                        source="{{ cloud_asset_url($file->transcodePlaylist()) }}"
                        caption="{{ $file->caption ? cloud_asset_url($file->caption->location('file.vtt')) : '' }}"
                        aspect-ratio="{{ $file->videoAspectRatio() }}"
                        poster="{{ imgix($file->transcodeThumbnail()) }}"
                    ></video-player>
                </div>
            </div>
        </div>
    @else
        <div class="preview-container">
            <video-modal
                file-id="video-{{ $file->id }}"
                source="{{ cloud_asset_url($file->transcodePlaylist()) }}"
                caption="{{ $file->caption ? cloud_asset_url($file->caption->location('file.vtt')) : '' }}"
                aspect-ratio="{{ $file->videoAspectRatio() }}"
                poster="{{ imgix($file->transcodeThumbnail()) }}"
                :video-player-height="{{ $file->videoHeight(current_account()->videoPlayerHeight) }}"
            >
                <template #modal-trigger="{ open }">
                    <div class="preview play-video" @click="open">
                        <img src="{{ imgix($file->transcodeThumbnail()) }}" alt="attachment-video-thumbnail">
                        <div class="icon"><i class="af-icons-md af-icons-md-circle-video"></i></div>
                    </div>
                </template>
            </video-modal>
        </div>
    @endif
@elseif ($file->transcodingStatus === 'error')
    <div class="preview-container no-preview">
        <div class="preview">
            <div class="icon">
                <i class="af-icons-md af-icons-md-circle-fail"></i><br>
                <span class="transcoding-status">@lang('files.transcoding.labels.failed')</span>
            </div>
        </div>
    </div>
    <div class="transcoding-error">
        @lang('files.transcoding.error')
        <br><br>
        {{-- TODO: Include ET error message--}}
        <div class="actions">
            <a href="javascript:;"
               data-errors="{{ encode_modal_content('entry.common.cards.previews.transcoding-errors', ['file' => $file]) }}"
               class="view-transcoding-errors">@lang('files.transcoding.error_view')</a> |
            <a href="{{ route('transcode.retry', $file) }}"
               class="ignore retry-transcoding">@lang('files.transcoding.actions.retry')</a>
        </div>
    </div>
@elseif (feature_disabled('transcoding') || !$file->transcodingStatus)
    <div class="preview-container no-preview">
        @include('entry.common.cards.previews.generic-file-preview', ['file' => $file->file])
    </div>
@else
    <div class="preview-container no-preview">
        <div class="preview">
            <div class="icon">
                <i class="af-icons-md af-icons-md-circle-processing af-icons-animate-rotate"></i><br>
                <span class="transcoding-status">@lang('files.transcoding.labels.in_progress')</span>
                <span
                    class="transcoding-status transcoding-status-ready hidden">@lang('files.transcoding.labels.ready')</span>
            </div>
        </div>
        <div id="video-player-modal" class="modal fade" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content"></div>
            </div>
        </div>
    </div>
@endif
