<?php
$searching = query_parameters(['category', 'status']);
?>

@section('title')
    {!! HTML::pageTitle(trans('entries.titles.entrant')) !!}
@stop

@section('main')
    <div class="entrant-page">
        @include('entry.common.header')

        @include('partials.holocron.feature-intro')

        {!! HTML::contentBlock('entrant-home') !!}

        <div class="entrant-content">
            @include('users.confirm')
            @include('users.invalid')

            <div class="entrant-results" id="my-entry-list">
                @if($formBoxEnabled)
                    @if($multiForm)
                        <forms-gallery-list :forms="@js($forms)" :routes="@jsObject($routes)"></forms-gallery-list>
                    @elseif($hasEntries)
                        <div>
                            {!! Button::link(route('entry.entrant.start'), trans('entries.titles.start'), ['type' => 'primary']) !!}
                        </div>
                    @endif
                @endif

                <my-entry-list :ids="@js($entryIds)" inline-template>
                    <div id="searchResults">
                        <div class="selectors island">
                            <div class="selector-title">
                                <div class="mrx">
                                    <h1>{!! trans('entries.titles.entrant') !!}</h1>
                                    @include('partials.holocron.feature-intro-revealer')
                                </div>
                                @if($hasEntries || $hasTrashedEntries)
                                    <div class="inline-block mtm -mlm">
                                        @include('partials.page.selectors.season')
                                        {!! html()->stateSelect('partials.page.selectors.state') !!}
                                    </div>
                                @endif
                                @if(!$hasEntries)
                                    <h2>{{ trans('entries.no_entries.title', ['name' => Auth::user()->name]) }}</h2>
                                    @if ($hasConfirmed && $entriesAllowed)
                                        <p>{!! trans('entries.no_entries.message') !!}</p>
                                    @endif
                                @endif

                                @if ($hasConfirmed && !$hasEntries && !$multiForm)
                                    @if ($entriesAllowed && Consumer::can('create', 'EntriesOwner'))
                                        <div>
                                            {!! Button::link(route('entry.entrant.start'), trans('entries.titles.start'), ['type' => 'primary']) !!}
                                        </div>
                                    @endif
                                @elseif(!$hasEntries && !$multiForm)
                                    <span class="btn btn-primary btn-lg disabled">{!! trans('entries.no_entries.button') !!}</span>
                                @endif

                                @if (Consumer::can('create', 'EntriesOwner') && !trashed_filter_active() && !archived_filter_active() && (!$isAtEntryLimit && !$entriesAllowed))
                                    <div class="selector-buttons buttons-spacer island">
                                        <span class="btn btn-primary btn-lg disabled">
                                            {!! trans('entries.titles.start') !!}
                                        </span>
                                        @if (!$entriesAllowed)
                                            @include('entry.entrant.entries-closed', ['message' => trans('entries.messages.closed')])
                                        @endif
                                    </div>
                                @endif
                            </div>
                            @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area', 'savedSearches'))
                            @endcomponent
                        </div>

                        @include('partials.errors.display')
                        @include('partials.errors.message')

                        @if($hasEntries || $hasTrashedEntries)
                            <div class="row mtm">
                                <div class="col-xs-12 col-lg-6">
                                    @if($entriesAllowed && $entries->count())
                                        <ul class="action-list-horizontal">
                                            <li>
                                                @includeWhen($canCopyEntries && !trashed_filter_active() && !archived_filter_active(), 'partials.list-actions.copy', ['resource' => 'entry.entrant', 'class' => 'action-button'])
                                            </li>
                                            <li>
                                                @includeWhen($canDeleteEntries && !archived_filter_active(), 'partials.list-actions.delete', ['resource' => 'entry.entrant', 'class' => 'action-button'])
                                            </li>
                                            <li>
                                                @includeWhen(feature_enabled('pdfs') && $canDownloadApplicationPdf, 'partials.list-actions.download', ['resource' => 'entry.entrant', 'class' => 'action-button'])
                                            </li>
                                        </ul>
                                    @endif
                                </div>
                                <div class="col-xs-12 col-lg-6">
                                    <div class="search-info">
                                        @include('partials.page.active-filters', ['filters' => Request::all()])
                                        @include('partials.page.pagination-info', ['paginator' => $entries])
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if ($entryPreviews)
                            <br>
                            <div class="alert-warning sticky island" role="alert">
                                <div class="icon">
                                    <i class="af-icons-md af-icons-md-alert-warning"></i>
                                </div>
                                <div class="message">
                                    @lang('entries.messages.entry-preview')
                                </div>
                            </div>
                        @endif

                        @include('entry.common.copying-entries')

                        <!-- Result set -->
                        <div>
                            @if ($entries->count())
                                @include('search.datatable', ['columnator' => $columnator, 'results' => $entries->items(), 'class' => 'entries-table'])

                                <div class="row">
                                    <div class="col-xs-12">
                                        @include('partials.page.pagination', ['paginator' => $entries])
                                    </div>
                                </div>
                            @elseif($hasEntries)
                                <div>
                                    <p>{!! trans('entries.table.empty') !!}</p>
                                </div>
                            @endif
                        </div>
                        <collaborators-list-modal></collaborators-list-modal>
                        <collaborators-invite-modal></collaborators-invite-modal>
                    </div>
                </my-entry-list>
            </div>
        </div>
    </div>
@stop
<script>
    import ArrowButton from '@/lib/components/Shared/ArrowButton';
    import FormsGalleryList from '@/modules/entries/components/FormsGalleryList';

    export default {
		components: {FormsGalleryList, ArrowButton}
	}
</script>
