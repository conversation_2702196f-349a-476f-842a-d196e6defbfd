<?php $searching = query_parameters(['review-stage', 'reviewer', 'decision']); ?>

@section('title')
    {!! HTML::pageTitle(trans('review-flow.titles.manage')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <review-list id="review-list" :ids="@js($taskIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('review-flow.titles.manage') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                </div>
                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                    @slot('actions')
                        {!! HTML::exportAction('manage_review_tasks.export') !!}
                        {!! HTML::broadcast('broadcast.new', 'reviewers') !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <portal-target name="reassign-reviewer" v-if="reassignReviewerRevealed" multiple></portal-target>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'review-flow.task.manage'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'review-flow.task.manage'])</li>
                            @endif
                            <li>
                                @include('partials.list-actions.reassign-reviewer', [
                                     'resource' => 'review-flow.task.manage',
                                     'params' => ['redirect' => route('review-flow.task.manage')]
                                 ])
                            </li>
                            <li>
                                @include('partials.list-actions.review-task-reset-action', ['resource' => 'review-flow.task.manage', 'params' => ['redirect' => route('review-flow.task.manage')]])
                            </li>
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">

                        @include('partials.page.active-filters', ['filters' => request()->all()])
                        @include('partials.page.pagination-info', ['paginator' => $tasks])
                    </div>
                </div>
            </div>

            <div>
                @if ($tasks->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $tasks->items(), 'class' => 'manage-review-tasks-table markers-table'])
                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $tasks])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('review-flow.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </review-list>
@stop
