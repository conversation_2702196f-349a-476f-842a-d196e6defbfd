@section('title')
    {!! HTML::pageTitle(trans('review-flow.tasks.title')) !!}
@stop

@section('main')
@include('partials.holocron.feature-intro')

<!-- Filtertron -->
<div class="filtertron">
    {!! html()->form('GET', url()->current())->attributes(['data-pjax'])->open() !!}
    <div class="search-container">
        <div class="keyword-search">
            <i class="af-icons af-icons-search"></i>
            <i class="arrow-down" id="expander"></i>
            {!! html()->label(trans('miscellaneous.search.placeholder'), 'keywords')->attributes(['class' => 'accessible-hidden']) !!}
            {!! html()->text('keywords', Request::get('keywords'))->attributes(['class' => 'form-control keywords', 'placeholder' => trans('miscellaneous.search.placeholder')]) !!}
        </div>
    </div>
    <div class="extended-container{{ query_parameters(['review-stage', 'decision']) ? ' visible' : '' }}">
        <div class="fields">
            <div class="form-group">
                {!! html()->label(trans('review-flow.table.columns.review_stage'), 'review-stage') !!}
                {!! html()->select('review-stage', $reviewStages, Request::get('review-stage'))->attributes(['class' => 'form-control']) !!}
            </div>
            <div class="form-group">
                {!! html()->label(trans('review-flow.table.columns.decision'), 'decision') !!}
                {!! html()->select('decision', $decisions, Request::get('decision'))->attributes(['class' => 'form-control']) !!}
            </div>

            @include('html.buttons.search')
        </div>
    </div>
    {!! html()->form()->close() !!}
</div>

<div id="searchResults"{!! query_parameters(['review-stage', 'decision']) ? ' class="courteous"' : '' !!}>
    <div class="row island">
        <div class="col-xs-12">
            <div class="selector-title">
                <div class="mrx">
                    <h1>{!! trans('review-flow.tasks.title') !!}</h1>
                    @include('partials.holocron.feature-intro-revealer')
                </div>
                <div class="inline-block mtm -mlm">
                    @include('partials.page.selectors.season')
                </div>
            </div>

            <div class="search-info">
                @include('partials.page.active-filters', ['filters' => Request::all()])
                @include('partials.page.pagination-info', ['paginator' => $reviewTasks])
            </div>
        </div>
    </div>

    <!-- Message -->
    @include('partials.errors.display')
    @include('partials.errors.message')

    <!-- Results set -->
    <div>
        @if ($reviewTasks->count())
            <table class="table entries-table markers-table">
                <thead>
                <tr>
                    <th class="thumbnail-column"></th>
                    @if ($canShowLocalIds)
                        <th>{!! HTML::sorter('local_id', trans('entries.table.columns.local_id')) !!}</th>
                    @endif
                    <th class="entries-title-column">{!! HTML::sorter('title', trans('entries.table.columns.title')) !!}</th>
                    <th>{{ trans('review-flow.table.columns.review_stage') }}</th>
                    <th>{{ trans('review-flow.table.columns.decision') }}</th>
                    <th>{!! HTML::sorter('submitted', trans('entries.table.columns.submitted')) !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($reviewTasks->items() as $reviewTask)
                    <tr>
                        <td>
                            @if (!empty($thumbnails[$reviewTask->entryId]))
                                <a href="{{ route('review-flow.task.view', ['reviewTask' => $reviewTask->token]) }}">
                                    @include('voting.common.thumbnail', ['thumbnail' => $thumbnails[$reviewTask->entryId], 'styling' => config('ui.images.thumbnail')])
                                </a>
                            @endif
                        </td>
                        @if ($canShowLocalIds)
                            <td class="entry-id">
                                @if ($reviewTask->shouldShowLocalId())
                                    <a href="{{ route('review-flow.task.view', ['reviewTask' => $reviewTask->token]) }}">{{ local_id($reviewTask->entry) }}</a>
                                @endif
                            </td>
                        @endif
                        <td>
                            <a href="{{ route('review-flow.task.view', ['reviewTask' => $reviewTask->token]) }}">{{ $reviewTask->entry->title }}</a>
                        </td>
                        <td>{{ $reviewTask->reviewStage->name }}</td>
                        <td>
                            @if ($reviewTask->actionTaken)
                                {{ $reviewTask->actionTaken === 'proceed' ? $reviewTask->reviewStage->proceedStatus : $reviewTask->reviewStage->stopStatus }}
                                (@lang('review-flow.status.'.$reviewTask->actionTaken))
                            @else
                                -
                            @endif
                        </td>
                        <td>
                            @if($reviewTask->entry && $reviewTask->entry->submittedAt)
                                {!! HTML::relativeTime($reviewTask->entry->submittedAt) !!}
                            @else
                                -
                            @endif
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>

            <div class="row">
                <div class="col-xs-12">
                    @include('partials.page.pagination', ['paginator' => $reviewTasks])
                </div>
            </div>
        @else
            <div>
                <p>{!! trans('entries.table.empty') !!}</p>
            </div>
        @endif
    </div>
</div>
@stop
