@section('title')
    {!! HTML::pageTitle(trans('review-flow.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <review-stage-list id="review-stage-list" :ids="@js($reviewStageIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{!! trans('review-flow.titles.main') !!}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter() !!}
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                    @include('partials.list-actions.add-resource', ['label' => trans('review-flow.titles.new'), 'route' => route('review-flow.new')])
                </div>
                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => false, 'disableAdvanced' => true])
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                  area="{{ $area }}"
                  title="{{ trans('search.shortcuts-bar.title') }}"
                  :saved-views="@js($savedViews)"
                  id="saved-views-shortcuts-bar"
                  class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            @if (trashed_filter_active())
                                <li>@include('partials.list-actions.delete', ['resource' => 'review-flow'])</li>
                            @else
                                <li>@include('partials.list-actions.delete', ['resource' => 'review-flow'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                </div>

                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => Request::all()])
                        @include('partials.page.pagination-info', ['paginator' => $reviewStages])
                    </div>
                </div>
            </div>

        <!-- Result set -->
            <div>
                @if ($reviewStages->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $reviewStages->items()])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $reviewStages])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('review-flow.table.empty')</p>
                    </div>
                @endif
            </div>
        </div>
    </review-stage-list>
@stop
