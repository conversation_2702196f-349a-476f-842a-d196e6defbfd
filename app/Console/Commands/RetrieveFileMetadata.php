<?php

namespace AwardForce\Console\Commands;

use AwardForce\Library\Filesystem\MetadataReader;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Files\Contracts\FileRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface as Log;

class RetrieveFileMetadata extends Command
{
    use ConsoleAccount;

    /** @var string */
    protected $signature = 'files:metadata
                                {account  : Restrict the command for a specific account slug}
                                {files    : Restrict the command for specific file ids separated by comma}';

    /** @var string */
    protected $description = 'Retrieve metadata for files';

    /** @var MetadataReader */
    protected $metadataReader;

    public function __construct(protected FileRepository $files, private Log $logger)
    {
        parent::__construct();
    }

    public function handle()
    {
        $fileIds = $this->argument('files');
        if (strlen($accountSlug = $this->argument('account')) != 8) {
            $this->error('Account argument should be a valid slug.');
            exit;
        }

        CurrentAccount::set($this->getAccount($accountSlug));

        $this->metadataReader = app(MetadataReader::class);
        $this->iterateFiles($this->files->getByIds(explode(',', $fileIds))->pluck('file'));
    }

    private function iterateFiles(Collection $files)
    {
        $files->each(function ($file) {
            $this->retrieveMetadata($file);
        });
    }

    private function retrieveMetadata(string $file)
    {
        if ($this->metadataReader->fromCache($file)->empty()) {
            $this->metadataReader->read($file, true);
        }

        $this->info("File {$file} finished");
    }
}
