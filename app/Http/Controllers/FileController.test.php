<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Library\Filesystem\Metadata;
use AwardForce\Modules\Files\Commands\UpdateFileMetadata;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Values\Size;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class FileControllerTest extends BaseTestCase
{
    use Laravel;

    private FileRepository|m\MockInterface $files;

    public function init()
    {
        \Bus::fake();
        app()->instance(FileRepository::class, $this->files = m::mock(FileRepository::class));
    }

    public function testItReturnsMetadataWithSizeAndModalObfuscatedString()
    {
        $file = new File;
        $file->metadata = new Metadata(size: $size = new Size(300, 300, 72));
        $this->files->shouldReceive('getById')
            ->with($fileId = 9890)
            ->andReturn($file)
            ->once();

        $controller = app(FileController::class);

        $metadata = $controller->metadata($fileId);

        $this->assertArrayHasKey('size', $metadata);
        $this->assertEquals((string) $size, $metadata['size']);
        $this->assertArrayHasKey('dataContent', $metadata);
        $this->assertIsString($metadata['dataContent']);
        $this->assertNotEmpty($metadata['dataContent']);

        \Bus::assertNotDispatched(UpdateFileMetadata::class);
    }

    public function testItUpdatesMetadataWhenIsMissing()
    {
        \Bus::shouldReceive('dispatchSync')
            ->with(m::type(UpdateFileMetadata::class), null)
            ->once()
            ->andReturn(new Metadata(size: $size = new Size(300, 300, 72)));
        $file = new File;
        $this->files->shouldReceive('getById')
            ->with($fileId = 9891)
            ->andReturn($file)
            ->once();

        $controller = app(FileController::class);

        $metadata = $controller->metadata($fileId);

        $this->assertEquals((string) $size, $metadata['size']);
    }
}
