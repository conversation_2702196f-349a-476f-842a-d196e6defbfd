<?php

namespace AwardForce\Http\Requests\File;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\LogsAuthRequests;
use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Files\Models\File;

class UploadRequest extends FormRequest
{
    use LogsAuthRequests;

    /**
     * When uploading files, only certain feature roles allow this feature. So, we'll
     * use the consumer layer to determine if the authorised consumer is one of these
     * feature roles, and do our required check as necessary.
     *
     * @return bool
     */
    public function authorize()
    {
        $allowed = ! is_null(Consumer::get()->user());

        $this->log($allowed ? 'allowed' : 'denied', 'upload', 'Files');

        return $allowed;
    }

    public function rules(): array
    {
        return [
            'name' => 'required',
            'original' => 'required',
            'resource' => ['required',
                function ($attribute, $value, $fail) {
                    if (! in_array($value, File::RESOURCES) &&
                        ! $this->isFieldOrAttachmentRequest()
                    ) {
                        $fail(trans('validation.upload.bad_resource', ['attribute' => $attribute]));
                    }
                }],
            'tabId' => ['numeric', 'nullable'],
            'resourceId' => ['numeric', 'nullable'],
            'foreignId' => ['numeric', 'nullable',
                function ($attribute, $value, $fail) {
                    if (! $this->isFieldOrAttachmentRequest()) {
                        $fail(trans('validation.upload.single_file_expected'));
                    }
                }],
            'language' => 'sometimes|nullable|string',
        ];
    }

    /**
     * Ensuring that logs can output the appropriate message.
     *
     * @return string
     */
    protected function layer()
    {
        return 'upload form request';
    }

    public function foreignId(): ?int
    {
        $foreignId = $this->get('foreignId');

        return is_numeric($foreignId) ? (int) $foreignId : null;
    }

    public function resourceId(): ?int
    {
        $resourceId = $this->get('resourceId');

        return is_numeric($resourceId) ? (int) $resourceId : null;
    }

    public function resource(): ?string
    {
        return $this->get('resource');
    }

    /**
     * checks if the request concerns a single upload file
     */
    public function isFieldOrAttachmentRequest(): bool
    {
        return (! empty($this->resourceId()) && $this->resource() === File::RESOURCE_FILE_FIELD.'-'.$this->resourceId())
            || in_array($this->resource(), $this->allowedResources());
    }

    private function allowedResources(): array
    {
        return [
            File::RESOURCE_ATTACHMENTS,
            File::RESOURCE_CAPTION,
            File::RESOURCE_COMMENTS,
        ];
    }

    public function tabId(): ?int
    {
        return $this->get('tabId');
    }
}
