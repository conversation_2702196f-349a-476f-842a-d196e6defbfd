<?php

namespace AwardForce\Modules\AIAgents\Services;

use AwardForce\Library\AIAgents\Contracts\TextGenerator;
use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use AwardForce\Modules\AIAgents\Boundary\AIAgent as AIAgentInterface;
use AwardForce\Modules\AIAgents\Boundary\FieldContext;
use AwardForce\Modules\AIAgents\Boundary\FieldTrigger;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\AIAgents\Events\AITokensConsumed;
use AwardForce\Modules\AIAgents\Exceptions\AIGenerationFailedException;
use Platform\Database\Eloquent\Model;
use Platform\Events\EventDispatcher;

final readonly class AIAgent implements AIAgentInterface
{
    use EventDispatcher;

    public function __construct(
        private ContextResolver $contextResolver,
        private TextGenerator $textGenerator,
    ) {
    }

    public function promptContext(Resource $resource, array $requiredContexts): PromptContext
    {
        return $this->contextResolver->generateFor($resource, $requiredContexts);
    }

    public function generateText(Resource $resource, Prompt $prompt, array $metaData = []): string
    {
        $response = $this->textGenerator->prompt($prompt);

        if ($response->failed()) {
            throw AIGenerationFailedException::fromResponse($response->error);
        }

        $this->dispatch(new AITokensConsumed($resource, $response->tokenUsage, $metaData));

        return $response->text;
    }

    public function model(Resource $resource): Model
    {
        return $this->contextResolver->composite($resource->type)->model($resource->id);
    }

    /**
     * @return FieldTrigger[]
     */
    public function triggers(ResourceType $resourceType): array
    {
        return $this->contextResolver->composite($resourceType)->triggers();
    }

    /**
     * @return FieldContext[]
     */
    public function contexts(ResourceType $resourceType): array
    {
        return $this->contextResolver->composite($resourceType)->contexts();
    }
}
