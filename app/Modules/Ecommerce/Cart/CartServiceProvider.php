<?php

namespace AwardForce\Modules\Ecommerce\Cart;

use AwardForce\Library\Providers\ModuleServiceProvider;
use AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\EntryVolumePricing;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\CartItemRepository;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\CartRepository;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\EloquentCartItemRepository;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\EloquentCartRepository;
use AwardForce\Modules\Ecommerce\Cart\Events\CartWasProcessed;
use AwardForce\Modules\Ecommerce\Cart\Events\ItemWasRemovedFromCart;
use AwardForce\Modules\Ecommerce\Cart\Exceptions\CartUnavailable;
use AwardForce\Modules\Ecommerce\Cart\Listeners\CartListener;
use AwardForce\Modules\Ecommerce\Cart\Services\RefreshCart;
use AwardForce\Modules\Ecommerce\Cart\Services\Refreshers\EntrantItemRefresher;
use AwardForce\Modules\Ecommerce\Cart\Services\Refreshers\EntryItemRefresher;
use AwardForce\Modules\Ecommerce\Cart\Services\Refreshers\SimpleItemRefresher;
use AwardForce\Modules\Ecommerce\Orders\Listeners\Payments;
use AwardForce\Modules\Entries\Events\EntryWasDeleted;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Payments\Events\DeletingPrice;
use AwardForce\Modules\Payments\Events\PaymentWasSuccessful;
use AwardForce\Modules\Payments\Events\PaymentWasUnsuccessful;
use AwardForce\Modules\Payments\Events\TaxDeleted;
use Illuminate\Contracts\Support\DeferrableProvider;

class CartServiceProvider extends ModuleServiceProvider implements DeferrableProvider
{
    protected array $repositories = [
        CartRepository::class => EloquentCartRepository::class,
        CartItemRepository::class => EloquentCartItemRepository::class,
    ];

    public function provides()
    {
        return [
            CartRepository::class,
            CartItemRepository::class,
        ];
    }

    protected array $files = [
        __DIR__.'/validators.php',
    ];
    protected array $listeners = [
        CartWasProcessed::class => [
            Payments::class.'@whenCartWasProcessed',
        ],
        PaymentWasSuccessful::class => [
            Payments::class.'@whenPaymentWasSuccessful',
            Payments::class.'@whenPaymentProcessFinished',
        ],
        PaymentWasUnsuccessful::class => [
            CartListener::class.'@whenPaymentWasUnsuccessful',
            Payments::class.'@whenPaymentWasUnsuccessful',
            Payments::class.'@whenPaymentProcessFinished',
        ],
        EntryWasDeleted::class => [
            CartListener::class.'@whenEntryWasDeleted',
        ],
        DeletingPrice::class => [
            CartListener::class.'@whenDeletingPrice',
        ],
        TaxDeleted::class => [
            CartListener::class.'@whenTaxDeleted',
        ],
        ItemWasRemovedFromCart::class => [
            CartListener::class.'@whenItemWasRemovedFromCart',
        ],
    ];

    public function register(): void
    {
        parent::register();

        $this->registerCart();
        $this->registerCartRefreshers();
        $this->registerSingletons();
    }

    private function registerCart()
    {
        $this->app->scoped(Cart::class, function ($app) {
            if (consumer_id()) {
                return $app->get(CartRepository::class)->forUser(consumer()->user(), current_account());
            }

            if (is_api_consumer()) {
                return Cart::open(new User);
            }

            throw new CartUnavailable('Cart requested by Consumer without a valid user ID');
        });
    }

    private function registerCartRefreshers()
    {
        $this->app->tag([
            EntryItemRefresher::class,
            EntrantItemRefresher::class,
            SimpleItemRefresher::class,
        ], 'cart.refresher');

        $this->app->scoped(RefreshCart::class);
    }

    private function registerSingletons()
    {
        $this->app->scoped(EntryVolumePricing::class);
    }
}
