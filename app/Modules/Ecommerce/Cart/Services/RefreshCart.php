<?php

namespace AwardForce\Modules\Ecommerce\Cart\Services;

use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\CartSupport;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\CartRepository;
use AwardForce\Modules\Ecommerce\Cart\Services\Refreshers\ItemRefresher;
use Exception;
use Illuminate\Container\Attributes\Tag;
use Platform\Events\EventDispatcher;
use Webmozart\Assert\Assert;

class RefreshCart
{
    use EventDispatcher;

    public function __construct(
        protected CartRepository $carts,
        #[Tag('cart.refresher')] private readonly iterable $refreshers,
    ) {
        Assert::allIsInstanceOf($this->refreshers, ItemRefresher::class);
    }

    public function refresh(Cart $cart): void
    {
        if ($cart->empty()) {
            return;
        }

        $this->refreshCurrency($cart);
        $this->refreshItems($cart);

        $this->carts->save($cart);

        $this->dispatch($cart->releaseEvents());
    }

    private function refreshCurrency(Cart $cart): void
    {
        if (in_array($cart->currency()->code(), current_account()->currencies->just('code'))) {
            return;
        }

        $cart->setCurrency(app(CartSupport::class)->currency());
    }

    private function refreshItems(Cart $cart): void
    {
        collect($cart->items())->each(function ($item, $key) use ($cart) {
            $refresher = $this->refresherFor($item);

            if ($refresher->shouldRemove($item, $cart)) {
                $cart->removeItemByKey($key, true);

                return;
            }

            $refresher->refresh($item, $cart);
        });
    }

    /**
     * @throws Exception
     */
    private function refresherFor($item): ItemRefresher
    {
        foreach ($this->refreshers as $refresher) {
            if ($refresher->canRefresh($item)) {
                return $refresher;
            }
        }

        $class = get_class($item);
        throw new Exception("No refresher found for {$class} class.");
    }
}
