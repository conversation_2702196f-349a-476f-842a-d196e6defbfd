<?php

namespace AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\EntryPriceVariants;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Payments\Models\PriceAmount;

class FieldVariant implements Variant
{
    use ChecksCategories;

    /**
     * Return true if the variant can return an amount for the conditions supplied.
     */
    public function appliesTo(PriceAmount $amount, Entry $entry): bool
    {
        return $this->chapterMatches($amount, $entry) &&
            $this->categoryMatches($amount, $entry) &&
            $this->fieldValueMatches($amount, $entry);
    }

    private function chapterMatches(PriceAmount $amount, Entry $entry): bool
    {
        return empty($amount->chapterId) || $amount->chapterId === $entry->chapterId;
    }

    private function categoryMatches(PriceAmount $amount, Entry $entry): bool
    {
        return empty($amount->categoryId) || $this->categoryApplies($amount->categoryId, $entry);
    }

    private function fieldValueMatches(PriceAmount $amount, Entry $entry): bool
    {
        $field = $this->entryFields($entry)->firstWhere('id', $amount->fieldId);

        return $field && $this->checkField($field, $amount);
    }

    private function entryFields(Entry $entry): Fields
    {
        return once(static fn(): Fields => $entry->fields);
    }

    private function checkField(Field $field, PriceAmount $amount): bool
    {
        if (! empty($field->value) && $field->optionable()) {
            return $this->checkOptionableField($field, $amount, $field->value);
        }

        if ($field->type === 'checkbox') {
            return $this->checkCheckboxField($amount, $field->value);
        }

        return $this->checkRegularField($amount, $field->value);
    }

    private function checkOptionableField(Field $field, PriceAmount $amount, $entryValue): bool
    {
        $options = $field->type === Field::TYPE_CHECKBOXLIST
            ? $entryValue
            : explode_options($entryValue);

        if (in_array($amount->fieldValue, $options)) {
            return true;
        }

        return false;
    }

    private function checkCheckboxField(PriceAmount $amount, $entryValue): bool
    {
        // Handle unchecked checkbox (value is falsy and amount expects 0)
        if (! $entryValue && $amount->fieldValue == 0) {
            return true;
        }

        // Handle checked checkbox (value is truthy and amount expects 1)
        if ($entryValue && $amount->fieldValue == 1) {
            return true;
        }

        return false;
    }

    private function checkRegularField(PriceAmount $amount, $entryValue): bool
    {
        return $entryValue && $entryValue === $amount->fieldValue;
    }
}
