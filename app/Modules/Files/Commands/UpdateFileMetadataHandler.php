<?php

namespace AwardForce\Modules\Files\Commands;

use AwardForce\Library\Filesystem\Metadata;
use AwardForce\Library\Filesystem\MetadataReader;
use AwardForce\Modules\Files\Contracts\FileRepository;

class UpdateFileMetadataHandler
{
    public function __construct(
        private MetadataReader $metadataReader,
        private FileRepository $files,
    ) {
    }

    public function handle(UpdateFileMetadata $command): ?Metadata
    {
        $file = $this->files->primary($command->fileId)->fields(['id', 'metadata', 'file'])->first();
        $file->metadata = $this->metadataReader->read($file->file, true);
        $this->files->save($file);

        return $file->metadata;
    }
}
