<?php

namespace AwardForce\Modules\Files\Services;

use Arr;
use AwardForce\Modules\Files\Exceptions\InvalidExifValueException;
use AwardForce\Modules\Files\Values\ExifMetadata;

class ExifParser
{
    public function parse(array $metadata): ExifMetadata
    {
        return new ExifMetadata(
            $this->iso($metadata),
            $this->normaliseRationalValue($metadata, 'Exif.ExposureTime'),
            $this->normaliseRationalValue($metadata, 'Exif.FocalLength'),
            $this->normaliseRationalValue($metadata, 'Exif.FNumber'),
            $this->datetime($metadata)
        );
    }

    private function iso(array $metadata): ?array
    {
        $iso = Arr::get($metadata, 'Exif.ISOSpeedRatings');

        return is_array($iso) ? $iso : [$iso];
    }

    private function datetime(array $metadata): ?string
    {
        return Arr::get($metadata, 'Exif.DateTimeOriginal') ?: Arr::get($metadata, 'Exif.DateTimeDigitized');
    }

    private function normaliseRationalValue(array $metadata, string $exifTagKey): ?string
    {
        $value = Arr::get($metadata, $exifTagKey);
        [$parsedValue, $error] = $this->parseTuple($value);

        if (! $this->valid($parsedValue) || ! empty($error)) {
            throw new InvalidExifValueException('Invalid EXIF rational value', [
                'value' => $value,
                'error' => $error,
                'tag' => $exifTagKey,
            ]);
        }

        return $parsedValue;
    }

    private function parseTuple(mixed $value): array
    {
        // parse only tuples
        if (! is_array($value) || count($value) !== 2) {
            return [$value, null];
        }

        [$numerator, $denominator] = $value;

        if (! is_numeric($numerator) || ! is_numeric($denominator)) {
            return [null, 'Non-numeric EXIF tuple'];
        }

        if ((float) $denominator === 0.0) {
            return [null, 'Division by zero in EXIF tuple'];
        }

        return [$numerator / $denominator, null];
    }

    protected function valid(mixed $value): bool
    {
        return is_null($value) || is_string($value) || is_numeric($value);
    }
}
