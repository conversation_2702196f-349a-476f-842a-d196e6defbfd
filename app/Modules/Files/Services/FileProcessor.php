<?php

namespace AwardForce\Modules\Files\Services;

use AwardForce\Library\Exceptions\FailedVirusScan;
use AwardForce\Library\Filesystem;
use AwardForce\Modules\Files\Exceptions\FileProcessingFailure;
use AwardForce\Modules\Files\Models\File;

class FileProcessor
{
    const PREFIX_FILE = 'files';

    /** @var string[]|ResourceValidator[] */
    private static $validators = [];

    /*** @var Filesystem\Storage */
    private $storage;

    /** @var Filesystem\Courier */
    private $courier;

    /** @var Filesystem\Inspector */
    private $inspector;

    protected $retrieveError;

    /** @var int */
    protected $tabId;

    private bool $throwNoValidatorExceptions = true;

    /** @param  string[]|ResourceValidator[]  $validators */
    public static function registerValidators(array $validators)
    {
        self::$validators = array_merge(self::$validators, $validators);
    }

    public function showNoValidatorExceptions(bool $value): self
    {
        $this->throwNoValidatorExceptions = $value;

        return $this;
    }

    public static function forgetValidators()
    {
        self::$validators = [];
    }

    public static function filename(string $extension): string
    {
        $hash = str_random(16);
        $hashDir = implode('/', array_slice(str_split($hash), 0, 6)).'/'.substr($hash, 6);

        return self::PREFIX_FILE.'/'.$hashDir.'/file.'.$extension;
    }

    public function __construct(Filesystem\Storage $storage)
    {
        $this->storage = $storage;
        $this->courier = $storage->courier();
        $this->inspector = $storage->inspector();
    }

    public function process(File $file)
    {
        try {
            info('FileProcessor - Retrieving file from storage');
            $localFile = $this->retrieve($file);
        } catch (\Exception $exception) {
            if ($file = $this->handleException($this->retrieveError, $file, false)) {
                return $file;
            }
        }

        try {
            info('FileProcessor - Refreshing metadata');
            $this->refreshMetadata($file, $localFile);

            info('FileProcessor - Performing validation');
            $this->validate($file, $localFile);

            info('FileProcessor - Scanning for viruses');
            $this->scan($file, $localFile);

            info('FileProcessor - Moving file to storage');
            $this->move($file, $localFile);

            info('FileProcessor - Setting file access control');
            $this->setAcl($file);
        } catch (FailedVirusScan $exception) {
            $this->handleException(File::STATUS_REJECTED_VIRUS_FOUND, $file, false);
        } catch (FileProcessingFailure $exception) {
            $this->handleException($exception->getMessage(), $file, false);
        }

        info('FileProcessor - Removing local copy of file');
        $this->removeLocal($localFile);
    }

    protected function retrieve(File $file)
    {
        $localFile = 'file-processing-'.str_random().'.'.file_extension($file->original);

        return $this->courier->copyToLocal($file->file, $localFile);
    }

    private function refreshMetadata($file, $localFile)
    {
        $file->update([
            'size' => $size = $this->inspector->size($localFile),
            'usageCount' => 1,
            'usageSize' => $size,
            'mime' => $this->inspector->mime($localFile),
        ]);
    }

    private function validate(File $file, string $localFile)
    {
        foreach (self::$validators as &$validator) {
            if (is_string($validator)) {
                $validator = app($validator);
            }

            if ($this->tabId) {
                $validator->forTab($this->tabId);
            }

            if (! $validator->validates($file)) {
                continue;
            }

            if ($validator->check($file)) {
                return;
            }

            throw FileProcessingFailure::failedValidation();
        }

        $this->throwNoValidatorExceptions && throw FileProcessingFailure::noValidator();
    }

    private function scan(File $file, string $localFile)
    {
        $context = $file->only('accountId', 'userId', 'resource', 'resourceId');

        $this->inspector->scan($localFile, $context);
    }

    protected function move(File $file, string $localFile)
    {
    }

    protected function removeRemote(string $file)
    {
        $this->storage->defaultDisk()->delete($file);
    }

    private function removeLocal(string $file)
    {
        $this->courier->removeLocal($file);
    }

    /**
     * Because all files are uploaded privately, we only set to public if required.
     *
     * @return mixed
     */
    private function setAcl(File $file)
    {
        if ($file->requiredAcl() === 'public') {
            return $this->courier->setAcl($file->file, $file->requiredAcl());
        }
    }

    /**
     * Hanldes file process exceptions
     *
     * @param  File|null  $file
     * @return File|null
     */
    protected function handleException(string $message, File $file, bool $removeRemote = true)
    {
        $file = $file->reject($message);

        if ($removeRemote) {
            $this->removeRemote($file->file);
            $file->file = '';
            $file->save();
        }

        return $file;
    }

    protected function courier()
    {
        return $this->courier;
    }
}
