<?php

namespace AwardForce\Modules\Files\Services;

use AwardForce\Modules\Files\Exceptions\InvalidExifValueException;
use PHPUnit\Framework\Attributes\TestWith;
use Str;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class ExifParserTest extends BaseTestCase
{
    use Laravel;

    private ExifParser $exifParser;

    protected function init()
    {
        $this->exifParser = new ExifParser();
    }

    #[TestWith(['2025-07-23 12:00:00', '2025-07-23 13:00:00', '2025-07-23 12:00:00'])] // priority to DateTimeOriginal
    #[TestWith([null, '2025-07-23 13:00:00', '2025-07-23 13:00:00'])] // fallback to DateTimeDigitized
    #[TestWith([null, null, null])] // both are null
    public function testItHandlesDatetime($original, $digitized, $expected): void
    {
        $metadata = [
            'Exif.DateTimeOriginal' => $original,
            'Exif.DateTimeDigitized' => $digitized,
        ];

        $exifData = $this->exifParser->parse($metadata);

        $this->assertEquals($expected, $exifData->datetime);
    }

    #[TestWith([null, [null]])]
    #[TestWith([400, [400]])]
    #[TestWith([[640], [640]])]
    public function testItHandlesIso($value, $expected): void
    {
        $metadata = [
            'Exif.ISOSpeedRatings' => $value,
        ];

        $exifData = $this->exifParser->parse($metadata);

        $this->assertEquals($expected, $exifData->iso);
    }

    public function testItReturnsArrayWithNullValueForIsoMissingValues(): void
    {
        $exifData = $this->exifParser->parse([]);

        $this->assertIsArray($exifData->iso);
        $this->assertCount(1, $exifData->iso);
        $this->assertNull($exifData->iso[0]);
    }

    #[TestWith([null, null])]
    #[TestWith([42, '42'])]
    #[TestWith(['3.14159', '3.14159'])]
    #[TestWith(['1/250', '1/250'])]
    #[TestWith([0.0054, '0.0054'])]
    public function testItDoesNotMutateValidRationalValues($value, $expectedValue): void
    {
        $exifTags = [
            'Exif.ExposureTime',
            'Exif.FocalLength',
            'Exif.FNumber',
        ];

        foreach ($exifTags as $tag) {
            $metadata = [$tag => $value];
            $exifData = $this->exifParser->parse($metadata);
            $tagName = lcfirst(Str::after($tag, '.'));

            $this->assertEquals($value, $exifData->{$tagName});
            $this->assertSame($expectedValue, $exifData->{$tagName});
        }
    }

    #[TestWith([1, 200])]
    #[TestWith([1, 50000])]
    #[TestWith([-2, 10])]
    #[TestWith([8, -2])]
    #[TestWith([10, 2])]
    public function testItResolvesValidTuples($numerator, $denominator): void
    {
        $exifTags = [
            'Exif.ExposureTime',
            'Exif.FocalLength',
            'Exif.FNumber',
        ];

        foreach ($exifTags as $tag) {
            $metadata = [$tag => [$numerator, $denominator]];
            $exifData = $this->exifParser->parse($metadata);
            $tagName = lcfirst(Str::after($tag, '.'));

            $this->assertEquals($numerator / $denominator, $exifData->{$tagName});
        }
    }

    public function testItReturnsNullForMissingValues(): void
    {
        $exifTags = [
            'exposureTime',
            'focalLength',
            'fNumber',
            'datetime',
        ];

        foreach ($exifTags as $exifTag) {
            $exifData = $this->exifParser->parse([]);

            $this->assertNull($exifData->{$exifTag});
        }
    }

    #[TestWith([[3, 0]])] // division by zero
    #[TestWith([[3, null]])] // non numeric tuple
    #[TestWith([[10, 'bar']])]
    #[TestWith([[10, true]])]
    #[TestWith([[5]])] // non tuple array
    public function testItThrowsExceptionForInvalidRationalValues($value): void
    {
        $this->expectException(InvalidExifValueException::class);

        $metadata = ['Exif.ExposureTime' => $value];
        $this->exifParser->parse($metadata);
    }
}
