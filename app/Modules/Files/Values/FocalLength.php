<?php

namespace AwardForce\Modules\Files\Values;

use AwardForce\Library\Values\Services\Transformable;
use AwardForce\Library\Values\Services\Transformer;
use Stringable;

readonly class FocalLength implements Stringable, Transformable
{
    use Transformer;

    public function __construct(public ?string $focalLength = null)
    {
    }

    public function __toString(): string
    {
        return $this->focalLength() ?? '';
    }

    private function focalLength(): ?string
    {
        return $this->focalLength ? $this->focalLength.' mm' : null;
    }
}
