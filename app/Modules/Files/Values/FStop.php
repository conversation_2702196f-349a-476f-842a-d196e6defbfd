<?php

namespace AwardForce\Modules\Files\Values;

use AwardForce\Library\Values\Services\Transformable;
use AwardForce\Library\Values\Services\Transformer;
use Stringable;

readonly class FStop implements Stringable, Transformable
{
    use Transformer;

    public function __construct(public ?string $fnumber = null)
    {
    }

    public function __toString(): string
    {
        return $this->fstop() ?? '';
    }

    private function fstop(): ?string
    {
        return $this->fnumber ? 'f/'.number_format($this->fnumber, 1, '.', '') : null;
    }
}
