<?php

namespace AwardForce\Modules\Entries\Events;

use Illuminate\Support\Facades\Route;
use Illuminate\Translation\Translator;
use Platform\Language\Language;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class ActiveFiltersListenerTest extends BaseTestCase
{
    use Laravel;

    public function testItShouldSetFullLanguageString()
    {
        Route::shouldReceive('is')->andReturn(true);
        $translator = app(Translator::class);
        $translator->setLocale('es_LA');
        $listener = app(ActiveFiltersListener::class);

        $filters = collect([
            'language' => (object) [
                'value' => 'es_LA',
                'text' => 'Language',
            ],
        ]);

        $listener->handle($filters, []);

        $language = new Language('es_LA');
        $this->assertEquals($language->language(), $filters->get('language')->value);
        $this->assertEquals(trans('setting.form.languages.language', locale: 'es_LA'), $filters->get('language')->text);
    }
}
