<?php

namespace AwardForce\Modules\Entries\Events;

use AwardForce\Library\Search\RouteAwareActiveFiltersListener;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use Illuminate\Support\Collection;
use Platform\Language\Language;

class ActiveFiltersListener extends RouteAwareActiveFiltersListener
{
    private $users;

    public function __construct(UserRepository $users)
    {
        $this->users = $users;
    }

    /**
     * Return an array of route names this listener applies to.
     *
     * @return array
     */
    protected function routes()
    {
        return ['entry.manager.index', 'entry.entrant.index', 'broadcast.new', 'grant.manager.index', 'broadcast.review'];
    }

    /**
     * Handle filters.
     */
    protected function handleFilters(Collection $filters, array $context)
    {
        if ($filters->has('moderation')) {
            $status = trans('entries.moderation.status.'.$filters->get('moderation')->value);
            $filters->get('moderation')->value = $status;
            $filters->get('moderation')->text = trans('entries.table.columns.moderation');
        }

        if ($filters->has('status')) {
            switch ($filterStatus = $filters->get('status')->value) {
                case 'in_progress':
                    $filters->get('status')->value = trans('entries.status.not_submitted');
                    break;
                default:
                    $filters->get('status')->value = trans('entries.status.'.$filterStatus);
            }

            $filters->get('status')->text = trans('entries.table.columns.status');
        }

        if ($filters->has('plagiarism-scan-status')) {
            $status = trans("entries.plagiarism_scan.status.{$filters->get('plagiarism-scan-status')->value}");
            $filters->get('plagiarism-scan-status')->value = $status;
            $filters->get('plagiarism-scan-status')->text = trans('entries.table.columns.plagiarism_scan');
        }

        if ($filters->has('entrant')) {
            $entrant = $this->users->getBySlugOrId($filters->get('entrant')->value);

            if ($entrant) {
                $filters->get('entrant')->value = $entrant->guest() ? trans('users.guest').' '.$entrant->slug : $entrant->fullName();
                $filters->get('entrant')->text = trans('entries.table.columns.entrant');
            }
        }

        if ($filters->has('eligibility_status') && $filterStatus = $filters->get('eligibility_status')?->value) {
            $filters->get('eligibility_status')->value = trans('entries.status.'.$filterStatus);
            $filters->get('eligibility_status')->text = trans('entries.table.columns.eligibility_status');
        }

        if ($filters->has('language')) {
            $filters->get('language')->value = (new Language($filters->get('language')->value))->language();
            $filters->get('language')->text = trans('setting.form.languages.language');
        }
    }
}
