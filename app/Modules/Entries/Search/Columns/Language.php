<?php

namespace AwardForce\Modules\Entries\Search\Columns;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Language implements Column
{
    public function title()
    {
        return trans('setting.form.languages.language');
    }

    public function name(): string
    {
        return 'user.language';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        $preferedLanguageCode = default_language_code();

        return DB::raw("COALESCE(memberships.language,'$preferedLanguageCode') as language");
    }

    public function value($record)
    {
        return $record->user->preferredLanguage()->language;
    }

    public function html($record)
    {
        return $this->value($record);
    }

    public function default(): Defaults
    {
        return new Defaults('export');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 1100;
    }

    public function sortable(): bool
    {
        return true;
    }
}
