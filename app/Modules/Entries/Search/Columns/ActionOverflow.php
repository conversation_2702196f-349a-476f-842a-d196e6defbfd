<?php

namespace AwardForce\Modules\Entries\Search\Columns;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Search\Actions\FormInvitationAction;
use AwardForce\Library\Search\Actions\HasAllocationTotals;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Integrations\Plagiarism\PlagiarismDetection;
use Illuminate\Support\HtmlString;
use Platform\Search\Columns\Columnator;

class ActionOverflow extends Columnator
{
    use HasAllocationTotals;

    public function __construct(string $area, protected ?PlagiarismDetection $plagiarismDetection = null)
    {
        parent::__construct($area, 'search.columnator.vue-button');
    }

    public function name(): string
    {
        return 'action-overflow';
    }

    /**
     * Returns valid HTML for search list views.
     */
    public function html($record): HtmlString
    {
        return new HtmlString(
            view(
                'entry.manager.search.action-overflow',
                [
                    'entry' => $record,
                    'plagiarismDetection' => $this->plagiarismDetection,
                    'selectedForm' => FormSelector::get(),
                    'allocationTotals' => $this->totals($record),
                    'canScheduleGrantReports' => $this->canScheduleGrantReports($record),
                    'invitationConfig' => $this->invitationConfig($record->entrant),
                ]
            )
        );
    }

    private function canScheduleGrantReports($record): bool
    {
        return ! $record->season->isArchived() && feature_enabled('grant_reports') && Consumer::can('create', 'Grants');
    }

    public function styles(): string
    {
        return 'condensed-column';
    }

    private function invitationConfig($entrant)
    {
        return once(fn() => (new FormInvitationAction('forms', 'Forms', true))->viewData($entrant));
    }
}
