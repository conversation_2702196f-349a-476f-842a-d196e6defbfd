<?php

namespace AwardForce\Modules\Entries\Search\Columns;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Illuminate\Support\HtmlString;

class MyEntriesLocalId extends LocalId
{
    /**
     * Returns valid HTML for search list views.
     *
     * @param  Entry  $entry
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($entry)
    {
        return new HtmlString(view('entry.entrant.search.local-id', [
            'localId' => $entry->form->settings->displayId ? $this->value($entry) : '',
            'entry' => $entry,
        ]));
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return FormSelector::selectedOrDefault()->settings->displayId;
    }
}
