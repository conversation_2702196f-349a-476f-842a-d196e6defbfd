<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Repositories\CollaboratorsRepository;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Services\SubmittableService;
use AwardForce\Modules\GrantReports\Commands\SaveValues;
use AwardForce\Modules\Grants\Repositories\GrantStatusRepository;
use AwardForce\Modules\Identity\Users\Models\User;

class Entries extends SubmittableService
{
    use SaveValues;

    public function __construct(
        protected AttachmentRepository $attachments,
        protected CollaboratorsRepository $collaborators,
        protected FieldRepository $fields,
        protected ValuesService $values,
        EntryRepository $entries
    ) {
        parent::__construct($attachments, $collaborators, $fields, $values);
        $this->submittable = $entries;
    }

    /**
     * update Entry with fieldValues
     *
     * @throws \AwardForce\Modules\Forms\Fields\Exceptions\CannotModifyFieldValue
     */
    public function updateEntry(
        int $entryId,
        int $userId,
        string $title,
        ?int $chapterId,
        ?int $categoryId,
        bool $isEntrant,
        ?array $values = [],
        ?int $timestamp = null,
        ?string $status = null,
        ?string $moderationStatus = null,
        ?string $grantStatus = null,
        ?string $grantEndDate = null,
        ?string $deadlineDate = null
    ): Entry {
        $entry = $this->submittable->getById($entryId);

        if (empty($status) && $entry->isInvited()) {
            $status = 'in_progress';
        }

        if ($status) {
            $this->applyStatus($entry, $status);
        }

        if ($moderationStatus) {
            $entry->moderate($moderationStatus);
        }

        if ($grantStatus !== null) {
            $entry->setGrantStatus(id_from_slug($grantStatus, app(GrantStatusRepository::class)));
        }

        if ($grantEndDate !== null) {
            $entry->setGrantEndDate(emptyToNull($grantEndDate));
        }

        if ($deadlineDate !== null) {
            $entry->setDeadline(emptyToNull($deadlineDate));
        }

        $entry->edit(
            $userId,
            $chapterId,
            $categoryId,
            $title
        );

        $this->saveValues($entry, $isEntrant, $timestamp, $values);

        return $entry;
    }

    /**
     * spawns a new Entry with fieldValues
     *
     *
     * @throws \AwardForce\Modules\Forms\Fields\Exceptions\CannotModifyFieldValue
     */
    public function startEntry(
        int $userId,
        string $title,
        int $seasonId,
        int $formId,
        ?int $chapterId,
        ?int $categoryId,
        ?array $values = [],
        ?bool $isEntrant = true,
        ?int $timestamp = null,
        ?string $status = null,
        ?string $moderationStatus = null,
        ?string $deadlineDate = null,
        ?string $deadlineTimezone = null,
        ?string $grantStatus = null,
        ?string $grantEndDate = null
    ): Entry {
        $entry = Entry::start(
            $userId,
            $title,
            $seasonId,
            $formId,
            $chapterId,
            $categoryId
        );

        if ($status) {
            $this->applyStatus($entry, $status);
        }

        if ($moderationStatus) {
            $entry->moderate($moderationStatus);
        }

        if ($grantStatus !== null) {
            $entry->setGrantStatus(id_from_slug($grantStatus, app(GrantStatusRepository::class)));
        }

        if ($grantEndDate !== null) {
            $entry->setGrantEndDate(emptyToNull($grantEndDate));
        }

        if ($deadlineDate) {
            $entry->setDeadline($deadlineDate, $deadlineTimezone);
        }

        $this->saveValues(
            $entry,
            $isEntrant,
            $timestamp,
            $values
        );

        return $entry;
    }

    private function applyStatus(Entry $entry, string $status)
    {
        match ($status) {
            'in_progress' => $entry->submitted() || $entry->isInvited() ? $entry->revertToInProgress() : null,
            'submitted' => $entry->submit(),
            'resubmission_required' => $entry->requireResubmission(),
            'resubmitted' => $entry->resubmit(),
            'invited' => $entry->invite(),
        };

        if ($status === 'invited') {
            $entry->invitedChapterId = $entry->chapterId;
            $entry->invitedCategoryId = $entry->categoryId;
        }

        if ($status == 'submitted') {
            $entry->submit();

            return;
        }

        if ($status == 'resubmission_required') {
            $entry->requireResubmission();

            return;
        }

        if ($status == 'resubmitted') {
            $entry->resubmit();

            return;
        }
    }

    /**
     * Update entry owner
     */
    public function updateEntryOwner(Entry $entry, User $user): Entry
    {
        $entry->edit(
            $user->id,
            $entry->chapterId,
            $entry->categoryId,
            $entry->title
        );

        $entry->save();

        return $entry;
    }

    public function formEditRoute(): string
    {
        return 'entry-form.manager.start';
    }
}
