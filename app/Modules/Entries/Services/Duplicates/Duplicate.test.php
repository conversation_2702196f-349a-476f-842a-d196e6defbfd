<?php

namespace AwardForce\Modules\Entries\Services\Duplicates;

use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Foundation\Testing\Concerns\InteractsWithTime;
use Platform\Events\EventDispatcher;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class DuplicateTest extends BaseTestCase
{
    use EventDispatcher;
    use InteractsWithTime;
    use Laravel;
    use \Tests\Concerns\Database;

    public function testItCanBePrimaryOrDuplicate(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicate($primary);

        $this->assertTrue($primary->primary());
        $this->assertNull($primary->parentId);

        $this->assertFalse($duplicate->primary());
        $this->assertEquals($primary->id, $duplicate->parentId);
    }

    public function testCanConfirmNotDuplicate(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $notDuplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $notDuplicate->markAsDuplicate($primary);

        $notDuplicate->confirmNotDuplicate();

        $this->assertTrue($notDuplicate->primary());
        $this->assertTrue($notDuplicate->confirmed());
    }

    public function testCanConfirmAndArchiveDuplicate(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicate($primary);

        $duplicate->confirmAndArchive($primary->entry);

        $this->assertEquals($primary->id, $duplicate->parentId);
        $this->assertTrue($duplicate->confirmed());
        $this->assertTrue($duplicate->entry->isArchived());
    }

    public function testCanUnarchiveNotDuplicate(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicate($primary);

        $duplicate->confirmAndArchive($primary->entry);

        $this->assertEquals($primary->id, $duplicate->parentId);
        $this->assertTrue($duplicate->entry->isArchived());

        $duplicate->unarchiveNotDuplicate();

        $this->assertNull($duplicate->parentId);
        $this->assertNull($duplicate->confirmedAt);
        $this->assertFalse($duplicate->entry->isArchived());
    }

    public function testCanForceGroupPrimary(): void
    {
        $oldPrimary = Duplicate::getForEntry($this->muffin(Entry::class));
        $newPrimary = Duplicate::getForEntry($this->muffin(Entry::class));
        $newPrimary->markAsDuplicate($oldPrimary);

        $newPrimary->forcePrimary();

        $this->assertTrue($newPrimary->primary());
        $this->assertTrue($newPrimary->confirmed());

        $this->assertFalse(($oldPrimary = $oldPrimary->fresh())->primary());
        $this->assertFalse($oldPrimary->confirmed());
        $this->assertEquals($newPrimary->id, $oldPrimary->parentId);
    }

    public function testChangeOfTitleMarksDuplicateOutOfDate(): void
    {
        $this->spy(Database::class);
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));

        $this->assertFalse($duplicate->outOfDate);

        ($entry = $duplicate->entry)->edit($entry->userId, $entry->chapterId, $entry->categoryId, 'new title')->save();
        $this->dispatch($entry->releaseEvents());

        $this->assertTrue($duplicate->fresh()->outOfDate);
    }

    public function testCanMarkAsDuplicateAndArchive(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicateAndArchive($primary);

        $this->assertEquals($primary->id, $duplicate->parentId);
        $this->assertTrue($duplicate->confirmed());
        $this->assertTrue($duplicate->entry->isArchived());
    }

    public function testPrimaryEntryIsSubmittedInDuplicate(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $this->assertFalse($primary->submitted());

        $primary->entry->submit();
        $this->assertTrue($primary->submitted());
    }

    public function testAllDuplicateEntryIdsHasAllIdsFromPrimaryAndDuplicate(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicate($primary);
        $ids = $primary->allDuplicateEntryIds();

        $this->assertTrue($ids->contains($primary->entry->id));
        $this->assertTrue($ids->contains($duplicate->entry->id));

        $ids = $duplicate->allDuplicateEntryIds();

        $this->assertTrue($ids->contains($primary->entry->id));
        $this->assertTrue($ids->contains($duplicate->entry->id));
    }

    public function testCanGetThePrimaryEntry(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicate($primary);

        $this->assertEquals($primary->entryId, $primary->primaryEntry()->id);
        $this->assertEquals($primary->entryId, $duplicate->primaryEntry()->id);
    }

    public function testAllAncestorEntryIds(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicate($primary);

        $ids = $primary->allAncestorEntryIds();

        $this->assertFalse($ids->contains($duplicate->entry->id));

        $ids = $duplicate->allAncestorEntryIds();

        $this->assertTrue($ids->contains($primary->entry->id));
    }

    public function testIgnoresForcePrimaryIfAlreadyPrimary(): void
    {
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));

        $primary->forcePrimary();
    }

    public function testRebuildScopedTreeAndDontTouchUpdatedTimestamps(): void
    {
        $this->travel(-5)->days();

        $season1 = $this->muffin(Season::class);
        $season2 = $this->muffin(Season::class);

        $duplicate1 = Duplicate::getForEntry($this->muffin(Entry::class, ['seasonId' => $season1->id]));
        $duplicate2 = Duplicate::getForEntry($this->muffin(Entry::class, ['seasonId' => $season2->id]));

        $timestamp1 = $duplicate1->updatedAt;
        $timestamp2 = $duplicate2->updatedAt;

        $this->assertTrue(Duplicate::isValidNestedSet());

        $duplicate1->lft = $duplicate2->rgt;
        $duplicate1->save();

        $duplicate2->lft = $duplicate1->rgt;
        $duplicate1->save();

        $this->assertFalse(Duplicate::isValidNestedSet());

        $this->travelBack();

        Duplicate::rebuildScopedTree(['season_id' => [$season1->id, $season2->id]], true);

        $this->assertTrue(Duplicate::isValidNestedSet());

        $this->assertEquals((string) $timestamp1, (string) $duplicate1->fresh()->updatedAt);
        $this->assertEquals((string) $timestamp2, (string) $duplicate2->fresh()->updatedAt);
    }

    public function testRebuildScopedTreeConsidersScopeWhenValidating(): void
    {
        $season1 = $this->muffin(Season::class);
        $season2 = $this->muffin(Season::class);

        $duplicate1 = Duplicate::getForEntry($this->muffin(Entry::class, ['seasonId' => $season1->id]));
        $duplicate2 = Duplicate::getForEntry($this->muffin(Entry::class, ['seasonId' => $season2->id]));

        $this->assertTrue(Duplicate::isValidNestedSet());

        $duplicate1->lft = $duplicate2->rgt;
        $duplicate1->save();

        $this->assertFalse(Duplicate::isValidScopedNestedSet(['season_id' => $season1->id]));
        $this->assertTrue(Duplicate::isValidScopedNestedSet(['season_id' => $season2->id]));

        Duplicate::rebuildScopedTree(['season_id' => $season1->id], true);

        $this->assertTrue(Duplicate::isValidScopedNestedSet(['season_id' => $season1->id]));
        $this->assertTrue(Duplicate::isValidScopedNestedSet(['season_id' => $season2->id]));
    }

    public function testDuplicateLockForUpdateShouldContaintSeasonAccountAndSkipLocked()
    {
        $season1 = CurrentAccount::activeSeason();
        $season2 = $this->muffin(Season::class);

        $entry1 = $this->muffin(Entry::class, ['seasonId' => $season1->id]);
        $entry2 = $this->muffin(Entry::class, ['seasonId' => $season2->id]);
        $duplicate1 = $this->muffin(Duplicate::class, ['season_id' => $season1->id, 'entry_id' => $entry1->id, 'lft' => 1, 'rgt' => 2]);
        $duplicate2 = $this->muffin(Duplicate::class, ['season_id' => $season2->id, 'entry_id' => $entry2->id, 'lft' => 1, 'rgt' => 2]);
        $query = $duplicate1->lockForUpdate();

        $lock = $query->getQuery()->lock;
        $wheres = $query->getQuery()->wheres;

        $this->assertEquals('FOR UPDATE SKIP LOCKED', $lock);
        $this->assertCount(1, $wheres);
        $this->assertEquals('season_id', $wheres[0]['column']);
        $this->assertEquals($season1->id, $wheres[0]['value']);
    }
}
