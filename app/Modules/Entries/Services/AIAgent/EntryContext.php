<?php

namespace AwardForce\Modules\Entries\Services\AIAgent;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\AIAgents\Boundary\Context;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Exceptions\MissingParameterException;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use Illuminate\Database\Eloquent\Relations\HasMany;

readonly class EntryContext implements Context
{
    public function __construct(
        private EntryRepository $entries,
        private ValuesService $valuesService,
    ) {
    }

    public function handle(PromptContext $promptContext, array $metaData): PromptContext
    {
        $entry = $this->entry($metaData);

        return $promptContext->fill([
            Vertical::replace('entry') => (object) [
                'form' => $entry->form->name,
                'title' => $entry->title,
                'category' => $entry->category->name,
                'parent_category' => $entry->category->parent?->name,
                'chapter' => $entry->chapter->name,
                'status' => $entry->submissionStatus(),
                'grant_status' => $entry->grantStatus?->name,
                'tags' => $entry->tags->just('tag'),
                'tabs' => $this->formatTabs($entry),
            ],
        ]);
    }

    /**
     * @param  string[]  $requiredContexts
     */
    public function applies(array $requiredContexts): bool
    {
        return in_array(AIFieldContext::Entry->value, $requiredContexts, true);
    }

    private function entry(array $metaData): Entry
    {
        $entryId = array_get($metaData, 'entry_id')
            ?? throw MissingParameterException::for('entry_id');

        return translate(
            $this->entries->primary($entryId)
                ->with($this->entryRelations())
                ->fields($this->entryFields())
                ->require()
        );
    }

    private function formatTabs(Entry $entry): array
    {
        $tabs = $entry->form->tabs;

        $allFields = new Fields($tabs->flatMap(fn(Tab $tab) => $tab->fields));

        $mappedFields = $this->valuesService->mapValuesToFields($entry, $allFields)
            ->groupBy('tab_id');

        return $tabs->map(
            fn(Tab $tab) => (object) [
                'name' => $tab->name,
                'type' => $tab->type,
                'fields' => collect($mappedFields->get($tab->id, []))->map(
                    fn(Field $field) => (object) [
                        'label' => $field->label,
                        'value' => $field->value,
                        'type' => $field->type,
                    ],
                )->all(),
            ])->all();
    }

    private function entryRelations(): array
    {
        return [
            'form:id',
            'category:id,parent_id',
            'category.parent:id',
            'chapter:id',
            'grantStatus:id',
            'tags:id,tag',
            'form:id',
            'form.tabs' => fn(HasMany $query) => $query
                ->select('id', 'form_id', 'type')
                ->with([
                    'fields' => fn(HasMany $query) => $query
                        ->where('protection', Field::PROTECTION_STANDARD) // filter out Personally Identifiable Information (PII) and other sensitive data.
                        ->select('id', 'slug', 'tab_id', 'type')
                        ->orderBy('order'),
                ])
                ->orderBy('order'),
        ];
    }

    /**
     * @return string[]
     */
    private function entryFields(): array
    {
        return [
            'id',
            'title',
            'form_id',
            'category_id',
            'chapter_id',
            'resubmitted_at',
            'resubmission_required_at',
            'submitted_at',
            'invited_at',
            'grant_status_id',
            'values',
            'protected',
            'hashes',
        ];
    }
}
