<?php

namespace AwardForce\Modules\Api\V2\Commands;

use AwardForce\Modules\Api\V2\Services\ApiFileUploadExceptionHandler;
use AwardForce\Modules\Files\Services\ApiFileProcessor;
use Platform\Events\EventDispatcher;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ApiProcessFileHandler
{
    use ApiFileUploadExceptionHandler;
    use EventDispatcher;

    /**
     * @throws \Exception
     */
    public function handle(ApiProcessFile $command): void
    {
        try {
            ApiFileProcessor::getInstance($command->data())->process($command->file());
        } catch (HttpException $e) {
            $this->sendEmailAndDeleteFile($command->file(), $e->getMessage());
        }

        $this->dispatch($command->file()->releaseEvents());
    }
}
