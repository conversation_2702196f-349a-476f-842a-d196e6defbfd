<?php

namespace AwardForce\Modules\Api\V2\Commands;

use AwardForce\Modules\Entries\Events\AttachmentsWereUpdated;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ApiProcessAttachmentHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;
    use NeedsBase64Image;

    protected function init(): void
    {
        Storage::fake();
    }

    public function testItCanProcessAttachment(): void
    {
        $form = $this->muffin(Form::class);
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);
        $file = File::prepare('aflogo.png', 'aflogo.png', File::RESOURCE_ATTACHMENTS, null, $entry->id);
        $tab = $this->muffin(Tab::class, ['type' => fn() => Tab::TYPE_ATTACHMENTS, 'form_id' => $form->id]);

        app(ApiProcessAttachmentHandler::class)
            ->handle(ApiProcessAttachment::getInstance($file, $this->base64Image(), $entry->id, $tab->id, 1));

        $this->assertCount(1, $entry->fresh()->attachments);
    }

    public function testItDispatchesAttachmentsWereUpdatedEvent(): void
    {
        $form = $this->muffin(Form::class);
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);
        $file = File::prepare('aflogo.png', 'aflogo.png', File::RESOURCE_ATTACHMENTS, null, $entry->id);
        $tab = $this->muffin(Tab::class, ['type' => fn() => Tab::TYPE_ATTACHMENTS, 'form_id' => $form->id]);
        Event::fake(AttachmentsWereUpdated::class);

        app(ApiProcessAttachmentHandler::class)
            ->handle(ApiProcessAttachment::getInstance($file, $this->base64Image(), $entry->id, $tab->id, 1));

        Event::assertDispatchedTimes(AttachmentsWereUpdated::class);
    }
}
