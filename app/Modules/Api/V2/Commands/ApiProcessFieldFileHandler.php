<?php

namespace AwardForce\Modules\Api\V2\Commands;

use AwardForce\Modules\Api\V2\Services\ApiFileUploadExceptionHandler;
use AwardForce\Modules\Files\Services\ApiFileProcessor;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use Platform\Events\EventDispatcher;
use Symfony\Component\HttpKernel\Exception\HttpException;

class ApiProcessFieldFileHandler
{
    use ApiFileUploadExceptionHandler;
    use EventDispatcher;

    public function __construct(private readonly ValuesService $values)
    {
    }

    public function handle(ApiProcessFieldFile $command): void
    {
        try {
            ApiFileProcessor::getInstance($command->data())->process($command->file());
        } catch (HttpException $e) {
            $this->sendEmailAndDeleteFile($command->file(), $e->getMessage());
        }

        $this->values->syncIndividualFieldValueForObject(
            $command->resource(),
            $command->field(),
            $command->file()->token,
        );

        $this->dispatch($command->file()->releaseEvents());
        $this->dispatch($command->resource()->releaseEvents());
    }
}
