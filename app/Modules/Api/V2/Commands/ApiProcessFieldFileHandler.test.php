<?php

namespace AwardForce\Modules\Api\V2\Commands;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Events\FieldValueUpdated;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class ApiProcessFieldFileHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;
    use NeedsBase64Image;

    protected function init(): void
    {
        Storage::fake();
    }

    public function testItCanProcessFileForField(): void
    {
        $file = $this->muffin(File::class);
        $entry = $this->muffin(Entry::class);
        $field = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_FILE, 'resource' => Field::RESOURCE_FORMS]);

        app(ApiProcessFieldFileHandler::class)
            ->handle(ApiProcessFieldFile::getInstance($file, $this->base64Image(), $entry, $field));

        $this->assertEquals([(string) $field->slug => $file->token], $entry->fresh()->values);
    }

    public function testItDispatchesFieldValueUpdatedEvent(): void
    {
        $file = $this->muffin(File::class);
        $entry = $this->muffin(Entry::class);
        $field = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_FILE, 'resource' => Field::RESOURCE_FORMS]);
        Event::fake(FieldValueUpdated::class);

        app(ApiProcessFieldFileHandler::class)
            ->handle(ApiProcessFieldFile::getInstance($file, $this->base64Image(), $entry, $field));

        Event::assertDispatchedTimes(FieldValueUpdated::class);
    }
}
