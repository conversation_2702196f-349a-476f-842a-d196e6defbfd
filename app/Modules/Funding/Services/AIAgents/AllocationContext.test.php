<?php

namespace AwardForce\Modules\Funding\Services\AIAgents;

use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Exceptions\MissingParameterException;
use AwardForce\Modules\AllocationPayments\Models\AllocationPayment;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\Fund;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class AllocationContextTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItSupportsAllocationContext(): void
    {
        $this->assertTrue(app(AllocationContext::class)->applies([AIFieldContext::Allocations->value]));

    }

    public function testItRequiresEntryId(): void
    {
        $context = app(AllocationContext::class);

        $this->expectException(MissingParameterException::class);

        $context->handle(new PromptContext(), []);
    }

    public function testItProvidesAllocations(): void
    {
        $fund = $this->muffin(Fund::class);
        $fund->saveTranslation('en_GB', 'name', 'Test Fund', current_account_id());
        $fund->saveTranslation('en_GB', 'description', 'Test description', current_account_id());
        $fund->save();

        $entry = $this->muffin(Entry::class);
        $allocation = $this->muffin(Allocation::class, [
            'entry_id' => $entry->id,
            'fund_id' => $fund->id,
        ]);
        $payment = $this->muffin(AllocationPayment::class, [
            'allocation_id' => $allocation->id,
            'entry_id' => $entry->id,
            'fund_id' => $fund->id,
        ]);

        $actual = app(AllocationContext::class)
            ->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $this->assertEquals([
            [
                'fund' => [
                    'name' => 'Test Fund',
                    'description' => 'Test description',
                    'currency' => $fund->currency->name(),
                    'budget_amount' => $fund->budget->value(),
                ],
                'allocated_amount' => $allocation->amount->value(),
                'paid_amount' => $payment->amount->value(),
            ],
        ], $actual->get('allocations'));
    }
}
