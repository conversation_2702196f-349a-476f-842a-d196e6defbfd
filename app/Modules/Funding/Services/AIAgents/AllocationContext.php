<?php

namespace AwardForce\Modules\Funding\Services\AIAgents;

use AwardForce\Modules\AIAgents\Boundary\Context;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Exceptions\MissingParameterException;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;

class AllocationContext implements Context
{
    public function __construct(
        private readonly AllocationRepository $allocations,
    ) {
    }

    public function handle(PromptContext $promptContext, array $metaData): PromptContext
    {
        $entryId = array_get($metaData, 'entry_id')
            ?? throw MissingParameterException::for('entry_id');

        return $promptContext->fill([
            'allocations' => $this->allocations($entryId),
        ]);
    }

    public function applies(array $requiredContexts): bool
    {
        return in_array(AIFieldContext::Allocations->value, $requiredContexts, true);
    }

    private function allocations(int $entryId): array
    {
        $allocations = translate($this->allocations
            ->entry($entryId)
            ->fields(['id', 'amount', 'fund_id'])
            ->with([
                'allocationPayments:amount,allocation_id,currency',
                'fund:id,currency,budget',
            ])
            ->get());

        return $allocations->map(function (Allocation $allocation) {
            return [
                'fund' => [
                    'name' => $allocation->fund->name,
                    'description' => $allocation->fund->description,
                    'currency' => $allocation->fund->currency->name(),
                    'budget_amount' => $allocation->fund->budget->value(),
                ],
                'allocated_amount' => $allocation->amount->value(),
                'paid_amount' => $allocation->allocationPayments->reduce(
                    fn($carry, $payment) => $carry + $payment->amount->value(),
                    0
                ),
            ];
        })->all();
    }
}
