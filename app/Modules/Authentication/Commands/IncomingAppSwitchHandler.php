<?php

namespace AwardForce\Modules\Authentication\Commands;

use AwardForce\Library\Authentication\Authenticator;
use AwardForce\Modules\Audit\Events\Auditor;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use Illuminate\Support\Facades\Session;
use Platform\Tokens\SwitchApp as SwitchAppToken;
use Platform\Tokens\TokenManager;

class IncomingAppSwitchHandler
{
    /** @var Authenticator */
    private $auth;

    /** @var UserRepository */
    private $users;

    /** @var TokenManager */
    private $tokens;

    public function __construct(Authenticator $auth, TokenManager $tokens, UserRepository $users)
    {
        $this->auth = $auth;
        $this->tokens = $tokens;
        $this->users = $users;
    }

    public function handle(IncomingAppSwitch $command)
    {
        /** @var SwitchAppToken $token */
        $token = $this->tokens->pull($command->token, SwitchAppToken::class);

        $user = $this->users->requireBy('global_id', $token->globalUserId);

        $this->auth->login($user);

        if ($token->secureSession) {
            Session::put(Auditor::SESSION_SWITCHER, $token->globalUserId);
        }
    }
}
