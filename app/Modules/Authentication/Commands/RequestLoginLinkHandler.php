<?php

namespace AwardForce\Modules\Authentication\Commands;

use AwardForce\Http\Middleware\Verification;
use AwardForce\Modules\Authentication\Events\LoginLinkWasRequested;
use AwardForce\Modules\Authentication\Services\SixDigitsCode;
use AwardForce\Modules\Authentication\Tokens\RequestLoginLinkToken;
use AwardForce\Modules\Authentication\Tokens\ResetPassword;
use AwardForce\Modules\Notifications\Data\ImportantNotification;
use AwardForce\Modules\Notifications\Services\Courier;
use AwardForce\Modules\Notifications\Services\Recipients\Factory;
use Illuminate\Support\Facades\URL;
use Platform\Events\EventDispatcher;
use Platform\Tokens\TokenManager;

class RequestLoginLinkHandler
{
    use EventDispatcher;

    /** @var RequestLoginLinkToken */
    private $tokenInstance;

    /** @var RequestLoginLink */
    private $command;

    public function __construct(private TokenManager $tokens, private SixDigitsCode $sixDigitsCode)
    {
    }

    /**
     * @return string
     */
    public function handle(RequestLoginLink $command)
    {
        $this->command = $command;

        $recipient = Factory::createFromString($command->channel, null, $this->getRecipientLanguageCode($command));
        $loginToken = null;
        if (! is_null($command->user)) {
            $loginLinkToken = $this->getLoginLinkToken();
            $loginLink = URL::route('login.link', [$loginLinkToken]);
            $loginToken = $this->sixDigitsCode->generateToken($this->command->user, $command->channel, $loginLink);
            $this->sendNotification($recipient, $loginLinkToken, $loginToken);
            session()->put(Verification::VERIFICATION_REDIRECTION_KEY, $loginLink);
        }

        $this->dispatch(new LoginLinkWasRequested);

        return $loginToken;
    }

    private function sendNotification($recipient, $loginLinkToken, $loginToken)
    {
        $loginUrl = URL::route('login.link', [$loginLinkToken]);
        $resetUrl = URL::route('password.reset', [$this->getPasswordResetLinkToken()]);

        $replace = [
            'account_name' => lang($this->command->account(), 'name'),
            'first_name' => $this->command->user->firstName,
            'verification_link' => $loginUrl,
            'account_url_text' => trans('auth.password.reset.url_text'),
            'expire' => $this->tokenInstance->expires(),
            'account_reset_password_url_text' => trans('auth.password.reset.title'),
            'reset_password_url' => $resetUrl,
            'code' => $code = $this->sixDigitsCode->tokenValue($loginToken)->code(),

            'account_url' => $loginUrl,
            'login_code' => $code,
        ];

        $notification = new ImportantNotification('auth.request_login_link', $replace);

        app(Courier::class)->deliver($notification, $recipient);
    }

    /**
     * @return string
     */
    private function getLoginLinkToken()
    {
        $this->tokenInstance = new RequestLoginLinkToken(
            $this->command->channel,
            $this->command->user->id,
            $this->command->account()->id,
            $this->command->redirect
        );

        return $this->tokens->create($this->tokenInstance);
    }

    /**
     * @return string
     */
    private function getPasswordResetLinkToken()
    {
        return $this->tokens->create(
            ResetPassword::request(
                $this->command->user->id,
                $this->command->account()->id,
                $this->command->redirect
            )
        );
    }

    /**
     * @return string
     */
    private function getRecipientLanguageCode(RequestLoginLink $command)
    {
        if (! is_null($command->user)) {
            return $command->user->preferredLanguageForUser($command->locale)->code;
        }

        return $command->locale;
    }
}
