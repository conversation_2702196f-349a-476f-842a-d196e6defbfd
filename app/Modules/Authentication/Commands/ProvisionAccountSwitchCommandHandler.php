<?php

namespace AwardForce\Modules\Authentication\Commands;

use AwardForce\Modules\Authentication\Middleware\Authenticator;
use AwardForce\Modules\Identity\Users\Services\UserGateway;
use Platform\Events\EventDispatcher;

class ProvisionAccountSwitchCommandHandler
{
    use EventDispatcher;

    /** @var UserGateway */
    private $gateway;

    public function __construct(UserGateway $gateway)
    {
        $this->gateway = $gateway;
    }

    public function handle(ProvisionAccountSwitchCommand $command)
    {
        return $this->gateway->generateSwitchAccountToken(
            $command->globalAccount->toString(),
            $command->globalUser->toString(),
            Authenticator::bypassChallenge(request())
        )['url'];
    }
}
