<?php

namespace AwardForce\Modules\Authentication\Middleware;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Audit\Events\Auditor;
use AwardForce\Modules\Authentication\Authenticators\Authenticator as AuthenticatorManager;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use AwardForce\Modules\Identity\Users\Services\Emulation\UserEmulator;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class Authenticator
{
    const CHALLENGE_SUCCESS = 'auth_a7ebc0a82b15728062d4952b72668f34'; // md5('authenticator.challenge.success')

    /**
     * @var array
     */
    private $setupRoutes = [
        'authenticator.index',
        'authenticator.register',
        'authenticator.reset',
        'profile.complete',
        'registration.complete',
        'profile.complete.forget',
        'profile.complete.role',
        'registration.complete.role',
        'profile.resend-token',
    ];

    /**
     * @var array
     */
    private static $exceptRoutes = [
        'authenticator.challenge',
        'authenticator.start',
        'authenticator.verify',
        'profile.ping',
        'profile.pusher',
        'logout',
        'registration.confirm',
        'cookies.accept',
        'updates.unread.count',
        'cart.items.count',
    ];

    /**
     * @var Manager
     */
    private $consumer;

    public function __construct(Manager $consumer)
    {
        $this->consumer = $consumer;
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response
     */
    public function handle(Request $request, Closure $next)
    {
        if (self::bypassChallenge($request)) {
            return $next($request);
        }

        if ($this->challengeRequired()) {
            return $this->redirectToChallenge($request);
        }

        if ($this->registrationRequired($request)) {
            return $this->redirectToRegister();
        }

        return $next($request);
    }

    /**
     * Determines if the consumer of the current request can bypass the second factor authentication challenge.
     */
    public static function bypassChallenge(Request $request): bool
    {
        return self::hasBypassSession($userId = consumer_id())
            || self::hasBypassCookie($request, $userId)
            || in_array($request->route()->getName(), self::$exceptRoutes);
    }

    public static function getChallengeSuccess(): string
    {
        return self::CHALLENGE_SUCCESS.'_'.crc32(consumer_id());
    }

    private function redirectToChallenge(Request $request): \Illuminate\Http\RedirectResponse
    {
        Session::put('intended', $request->fullUrl());

        return redirect()->route('authenticator.challenge');
    }

    private function redirectToRegister(): \Illuminate\Http\RedirectResponse
    {
        return redirect()->route('authenticator.index');
    }

    private function challengeRequired(): bool
    {
        return (new AuthenticatorManager($this->consumer->user()))->challengeRequired();
    }

    private function registrationRequired(Request $request)
    {
        return $this->consumer->isUser()
            && $this->consumer->get()->authenticatorRequired()
            && ! in_array($request->route()->getName(), $this->setupRoutes);
    }

    private static function hasBypassSession($userId): bool
    {
        return consumer()->isGuest()
            || (int) Session::get(self::getChallengeSuccess()) === $userId
            || (int) Session::get(Auditor::SESSION_SWITCHER) === $userId
            || JediEmulator::active()
            || UserEmulator::active();
    }

    private static function hasBypassCookie(Request $request, $userId): bool
    {
        $cookie = json_decode(urldecode($request->cookie(self::getChallengeSuccess(), '')), true);

        return is_array($cookie)
            && (int) $cookie['id'] === $userId
            && Carbon::createFromTimestamp($cookie['expires'])->isFuture();
    }
}
