<?php

namespace AwardForce\Modules\GrantReports\Services\AIAgent;

use AwardForce\Modules\AIAgents\Boundary\Context;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Exceptions\MissingParameterException;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use Illuminate\Database\Eloquent\Relations\HasMany;

readonly class GrantReportContext implements Context
{
    public function __construct(
        private GrantReportRepository $grantReports,
        private ValuesService $valuesService,
    ) {
    }

    public function handle(PromptContext $promptContext, array $metaData): PromptContext
    {
        $entryId = array_get($metaData, 'entry_id')
            ?? throw MissingParameterException::for('entry_id');

        return $promptContext->fill([
            'grant_reports' => $this->grantReports($entryId),
        ]);
    }

    public function applies(array $requiredContexts): bool
    {
        return in_array(AIFieldContext::GrantReports->value, $requiredContexts, true);
    }

    private function grantReports(int $entryId): array
    {
        $grantReports = translate($this->grantReports
            ->fields(['id', 'form_id', 'values', 'protected', 'hashes'])
            ->with([
                'form:id',
                'form.tabs' => fn(HasMany $query) => $query
                    ->select('id', 'form_id', 'type')
                    ->with([
                        'fields' => fn(HasMany $query) => $query
                            ->where('protection', Field::PROTECTION_STANDARD) // filter out Personally Identifiable Information (PII) and other sensitive data.
                            ->select('id', 'slug', 'tab_id', 'type')
                            ->orderBy('order'),
                    ])
                    ->orderBy('order'),
            ])
            ->entry($entryId)
            ->get()
        );

        return $grantReports->map(function (GrantReport $report) {

            $tabs = $report->form->tabs;

            $allFields = new Fields($tabs->flatMap(fn(Tab $tab) => $tab->fields));

            $mappedFields = $this->valuesService->mapValuesToFields($report, $allFields)
                ->groupBy('tab_id');

            return [
                'form' => [
                    'name' => $report->form->name,
                ],
                'tabs' => $tabs->map(fn(Tab $tab) => [
                    'name' => $tab->name,
                    'type' => $tab->type,
                    'fields' => collect($mappedFields->get($tab->id, []))->map(
                        fn(Field $field) => [
                            'label' => $field->label,
                            'value' => $field->value,
                            'type' => $field->type,
                        ],
                    )->all(),
                ])->all(),
            ];
        })->all();
    }
}
