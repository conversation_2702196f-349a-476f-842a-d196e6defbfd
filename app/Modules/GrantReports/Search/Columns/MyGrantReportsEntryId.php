<?php

namespace AwardForce\Modules\GrantReports\Search\Columns;

use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Platform\Search\Defaults;

class MyGrantReportsEntryId extends EntryId
{
    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return FormSelector::selectedOrDefault()->settings->displayId;
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function html($record): string
    {
        if ($record->entry->form->settings->displayId) {
            return $this->value($record);
        }

        return '';
    }
}
