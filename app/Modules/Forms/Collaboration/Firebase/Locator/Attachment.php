<?php

namespace AwardForce\Modules\Forms\Collaboration\Firebase\Locator;

use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable as SubmittableModel;

final readonly class Attachment extends BaseLocator
{
    public const string COLLECTION = 'attachments';

    public function __construct(private SubmittableModel $submittable)
    {
    }

    public function documentParts(): array
    {
        return (new Submittable($this->submittable))->documentParts();
    }
}
