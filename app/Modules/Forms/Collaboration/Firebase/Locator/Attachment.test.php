<?php

namespace AwardForce\Modules\Forms\Collaboration\Firebase\Locator;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Eloquence\Behaviours\Slug;
use Tests\BaseTestCase;

class AttachmentTest extends BaseTestCase
{
    public function testItGeneratesCorrectPath(): void
    {
        $form = new Form;
        $form->slug = Slug::random();
        $submittable = new Entry;
        $submittable->slug = Slug::random();
        $submittable->setRelation('form', $form);
        $locator = new Attachment($submittable);

        $path = $locator->path('testFieldPath');

        $this->assertSame('attachments', $path->collection);
        $this->assertSame("{$form->slug}-{$submittable->slug}", $path->documentId);
        $this->assertSame('testFieldPath', $path->fieldPath);
    }
}
