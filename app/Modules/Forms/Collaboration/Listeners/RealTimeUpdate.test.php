<?php

namespace AwardForce\Modules\Forms\Collaboration\Listeners;

use AwardForce\Library\Authorization\ApiConsumer;
use AwardForce\Library\Database\Firebase\Database as FirebaseDatabase;
use AwardForce\Library\Encrypter\Strategies\Encrypter;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Events\AttachmentsWereUpdated;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Collaboration;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Referees\Events\EmailChanged;
use AwardForce\Modules\Referees\Events\NameChanged;
use AwardForce\Modules\Referees\Models\Referee;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use Platform\Events\EventDispatcher;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class RealTimeUpdateTest extends BaseTestCase
{
    use Database;
    use EventDispatcher;
    use Laravel;

    protected FirebaseDatabase|MockInterface $database;
    protected Encrypter|MockInterface $encryptionStrategy;
    protected Collaboration|MockInterface $collaboration;

    public function init(): void
    {
        $this->database = $this->spy(FirebaseDatabase::class);
        $this->encryptionStrategy = $this->spy(Encrypter::class);
        $this->collaboration = $this->spy(Collaboration::class);

        Feature::shouldReceive('enabled')->with('collaboration')->andReturnFalse()->byDefault();
        Feature::shouldReceive('enabled')->with('api_submission_updates')->andReturnFalse()->byDefault();
    }

    public function testDoesNotUpdateIfApiUpdatesAreDisabled(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => false])]);

        app(ValuesService::class)->syncIndividualFieldValueForObject(
            $entry = $this->muffin(Entry::class, ['form_id' => $form->id]),
            $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS]),
            'some value'
        );

        $this->dispatch($entry->releaseEvents());

        $this->database->shouldNotHaveReceived('set');
        $this->encryptionStrategy->shouldNotHaveReceived('encrypt');
    }

    public function testDoesNotUpdateIfUserIsNotApiConsumer(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        app(ValuesService::class)->syncIndividualFieldValueForObject(
            $entry = $this->muffin(Entry::class, ['form_id' => $form->id]),
            $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS]),
            'some value'
        );

        $this->dispatch($entry->releaseEvents());

        $this->database->shouldNotHaveReceived('set');
        $this->encryptionStrategy->shouldNotHaveReceived('encrypt');
    }

    public function testDoesNotUpdateIfApiSubmissionUpdatesFeatureIsDisabled(): void
    {
        \Consumer::set(new ApiConsumer);
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        app(ValuesService::class)->syncIndividualFieldValueForObject(
            $entry = $this->muffin(Entry::class, ['form_id' => $form->id])->fresh(),
            $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS]),
            'some value'
        );

        $this->dispatch($entry->releaseEvents());

        $this->database->shouldNotHaveReceived('set');
        $this->encryptionStrategy->shouldNotHaveReceived('encrypt');
    }

    public function testUpdatesIfApiUpdatesAreEnabledAndUpdateIsMadeByApiConsumer(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        app(ValuesService::class)->syncIndividualFieldValueForObject(
            $entry = $this->muffin(Entry::class, ['form_id' => $form->id])->fresh(),
            $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS]),
            'some value'
        );

        $this->dispatch($entry->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesContributorFields(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);
        $contributorsTab = $this->muffin(Tab::class, ['type' => Tab::TYPE_CONTRIBUTORS, 'form_id' => $form->id]);

        app(ValuesService::class)->syncIndividualFieldValueForObject(
            $contributor = $this->muffin(Contributor::class, ['submittable_id' => $entry->id, 'tab_id' => $contributorsTab])->fresh(),
            $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_CONTRIBUTORS]),
            'some value'
        );

        $this->dispatch($contributor->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesAttachmentFields(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);
        $attachmentsTab = $this->muffin(Tab::class, ['type' => Tab::TYPE_ATTACHMENTS, 'form_id' => $form->id]);

        app(ValuesService::class)->syncIndividualFieldValueForObject(
            $attachment = $this->muffin(Attachment::class, ['submittable_id' => $entry->id, 'tab_id' => $attachmentsTab])->fresh(),
            $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_ATTACHMENTS]),
            'some value'
        );

        $this->dispatch($attachment->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesEntryTitle(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id])->fresh();
        $entry->editTitle('new title');

        $this->dispatch($entry->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesEntryCategory(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id])->fresh();
        $entry->editCategory($this->muffin(Category::class, ['form_id' => $form->id])->id);

        $this->dispatch($entry->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesEntryChapter(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id])->fresh();
        $entry->editChapter($this->muffin(Chapter::class)->id);

        $this->dispatch($entry->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesRefereeField(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);

        app(ValuesService::class)->syncIndividualFieldValueForObject(
            $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->id])->fresh(),
            $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_REFEREES]),
            'some value'
        );

        $this->dispatch($referee->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesRefereeName(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);
        Event::fake(NameChanged::class);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->id]);
        $referee->update(['name' => str_random()]);

        $this->dispatch($referee->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesRefereeEmail(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);
        Event::fake(EmailChanged::class);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->id]);
        $referee->update(['email' => '<EMAIL>']);

        $this->dispatch($referee->releaseEvents());

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
        $this->collaboration->shouldHaveReceived('updateFlagSubmittable')->once();
    }

    public function testUpdatesAttachmentsWhenAttachmentsWereUpdated(): void
    {
        \Consumer::set(new ApiConsumer);
        Feature::shouldReceive('enabled')->with('api_submission_updates')->once()->andReturnTrue();
        Feature::shouldReceive('enabled')->with('image_optimisation')->andReturnTrue();

        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);
        $attachmentsTab = $this->muffin(Tab::class, ['type' => Tab::TYPE_ATTACHMENTS, 'form_id' => $form->id]);

        $this->muffin(Attachment::class, [
            'submittable_id' => $entry->id,
            'tab_id' => $attachmentsTab->id,
        ]);

        $this->dispatch(new AttachmentsWereUpdated($entry));

        $this->database->shouldHaveReceived('set')->once();
        $this->encryptionStrategy->shouldHaveReceived('encrypt')->once();
    }
}
