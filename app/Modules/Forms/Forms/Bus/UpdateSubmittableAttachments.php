<?php

namespace AwardForce\Modules\Forms\Forms\Bus;

use Carbon\Carbon;

class UpdateSubmittableAttachments
{
    public function __construct(
        protected string $type,
        protected int $submittableId,
        protected array $attachmentFields,
        protected Carbon $timestamp,
        protected ?int $order = null,
        protected ?int $tabId = null,
        protected ?int $fileId = null
    ) {
    }

    public function submittableId(): int
    {
        return $this->submittableId;
    }

    public function attachmentFields(): array
    {
        return $this->attachmentFields;
    }

    public function timestamp(): int
    {
        return $this->timestamp->getTimestamp();
    }

    public function order(): ?int
    {
        return $this->order;
    }

    public function tabId(): ?int
    {
        return $this->tabId;
    }

    public function fileId(): ?int
    {
        return $this->fileId;
    }

    public function type(): string
    {
        return $this->type;
    }
}
