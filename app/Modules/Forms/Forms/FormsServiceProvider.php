<?php

namespace AwardForce\Modules\Forms\Forms;

use AwardForce\Library\Providers\ModuleServiceProvider;
use AwardForce\Modules\Entries\Events\AttachmentsWereUpdated;
use AwardForce\Modules\Entries\Events\CategoryWasChanged;
use AwardForce\Modules\Entries\Events\ChapterWasChanged;
use AwardForce\Modules\Entries\Events\TitleWasChanged;
use AwardForce\Modules\Forms\Collaboration\Listeners\RealTimeUpdates;
use AwardForce\Modules\Forms\Fields\Events\FieldValueUpdated;
use AwardForce\Modules\Forms\Forms\Database\Repositories\EloquentFormRepository;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Events\FormWasDeleted;
use AwardForce\Modules\Forms\Forms\Events\Listeners\ResetSelected;
use AwardForce\Modules\Forms\Forms\Http\Middleware\ExceptionHandler;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Forms\Services\FormSelectorService;
use AwardForce\Modules\Referees\Events\EmailChanged;
use AwardForce\Modules\Referees\Events\NameChanged;
use Illuminate\Contracts\Support\DeferrableProvider;

class FormsServiceProvider extends ModuleServiceProvider implements DeferrableProvider
{
    protected array $repositories = [
        FormRepository::class => EloquentFormRepository::class,
    ];
    protected $aliases = [
        'FormSelector' => FormSelector::class,
    ];
    protected array $files = [
        __DIR__.'/macros.php',
    ];
    protected array $listeners = [
        FieldValueUpdated::class => RealTimeUpdates::class.'@whenFieldValueWasUpdated',
        CategoryWasChanged::class => RealTimeUpdates::class.'@whenCategoryOrChapterWasChanged',
        ChapterWasChanged::class => RealTimeUpdates::class.'@whenCategoryOrChapterWasChanged',
        TitleWasChanged::class => RealTimeUpdates::class.'@whenTitleWasChanged',
        NameChanged::class => RealTimeUpdates::class.'@whenRefereeNameChanged',
        EmailChanged::class => RealTimeUpdates::class.'@whenRefereeEmailChanged',
        AttachmentsWereUpdated::class => RealTimeUpdates::class.'@whenAttachmentsUpdated',
        FormWasDeleted::class => ResetSelected::class,
    ];

    public function register(): void
    {
        parent::register();

        $this->registerFormSelector();
        $this->aliasMiddleware('form.exceptions', ExceptionHandler::class);
    }

    protected function registerFormSelector()
    {
        $this->app->scoped('form.selector', FormSelectorService::class);
    }

    public function provides()
    {
        return [
            FormRepository::class,
        ];
    }
}
