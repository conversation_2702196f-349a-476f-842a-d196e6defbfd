<?php

namespace AwardForce\Modules\Forms\Forms\Services;

use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Services\Entries;
use AwardForce\Modules\Forms\Collaboration\Repositories\CollaboratorsRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Repositories\SubmittableRepository;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Services\GrantReports;
use Illuminate\Support\Facades\DB;
use Platform\Events\EventDispatcher;

abstract class SubmittableService
{
    use EventDispatcher;

    protected SubmittableRepository $submittable;
    public static array $repositories = [
        Form::FORM_TYPE_ENTRY => EntryRepository::class,
        Form::FORM_TYPE_REPORT => GrantReport::class,
    ];

    public function __construct(
        protected AttachmentRepository $attachments,
        protected CollaboratorsRepository $collaborators,
        protected FieldRepository $fields,
        protected ValuesService $values,
    ) {
    }

    abstract public function formEditRoute(): string;

    /**
     * Order all attachments of an entry (in all Attachments tabs) according to passed in ordered fileId list.
     *
     * @param  array  $orderedFileIds mapping of all files to their orders
     *
     * @throws \Throwable
     */
    public function orderAttachments(array $orderedFileIds): void
    {
        $allAttachments = $this->attachments->getByFileIds(array_keys($orderedFileIds))->keyBy('file_id');
        $tabIds = $allAttachments->pluck('tabId')->unique();
        asort($orderedFileIds, SORT_NUMERIC);

        DB::transaction(function () use ($allAttachments, $orderedFileIds, $tabIds) {
            foreach ($tabIds as $tabId) {
                $order = 1;

                // Sort attachments from 1 to N for every tab separately (order can repeat across tabs)
                foreach (array_keys($orderedFileIds) as $fileId) {
                    if ($attachment = $allAttachments->get($fileId)) {
                        if ($attachment->tabId != $tabId) {
                            continue;
                        }

                        if ($attachment->order != $order) {
                            $attachment->order = $order;
                            $attachment->save();
                        }

                        $order++;
                    }
                }
            }
        });

        $this->dispatch(array_unique($allAttachments->releaseEvents()));
    }

    public function updateAttachmentTab(int $fileId, int $tabId): void
    {
        $attachment = $this->attachments->getByFileIds([$fileId])->first();
        $attachment->tabId = $tabId;

        $this->attachments->save($attachment);
    }

    public function updateContributors(Submittable $submittable, array $values, ?int $timestamp = null)
    {
        $validContributorsIds = [];
        foreach ($values as $contributorId => $contributorValues) {
            if ($contributor = $submittable->contributors->find($contributorId)) {
                $this->updateContributor($contributor, $contributorValues, $timestamp);
                $validContributorsIds[] = $contributorId;
            }
        }

        $submittable->contributors->whereNotIn('id', $validContributorsIds)->each(
            function (Contributor $contributor) {
                $contributor->delete();
            }
        );
    }

    public function updateContributor(Contributor $contributor, array $values, ?int $timestamp = null): void
    {
        $this->values->syncValuesForObject($values, $contributor, $timestamp);
    }

    public function createContributor(Submittable $submittable, array $values, int $tabId, ?int $timestamp = null): int
    {
        $contributor = $submittable->contributors()->create(['tab_id' => $tabId]);
        $this->values->setValuesForObject($values, $contributor, $timestamp);

        return $contributor->id;
    }

    public function createReferee(Submittable $submittable, array $values, int $tabId, ?int $timestamp = null): int
    {
        $referee = $submittable->referees()->create(['tab_id' => $tabId]);
        $this->values->setValuesForObject($values, $referee, $timestamp);

        return $referee->id;
    }

    public function setSubmittableAttachments(int $submittableId, array $attachmentFields, ?int $timestamp = null): Submittable
    {
        $submittable = $this->submittableById($submittableId);

        $fields = $this->fields->slugs(array_merge(...array_map('array_keys', $attachmentFields)))
            ->fields(['*'])
            ->get();
        $submittable->attachments->each(function ($attachment) use ($fields, $attachmentFields) {
            if ($this->shouldSyncAttachmentFieldValues($attachmentFields, $attachment)) {
                $fields->filter(fn (Field $field) => array_key_exists($field->slug, $attachmentFields[$attachment->fileId]))
                    ->each(fn (Field $field) => $this->values->syncIndividualFieldValueForObject(
                        object: $attachment,
                        field: $field,
                        value: array_get($attachmentFields[$attachment->fileId], $field->slug),
                    ));

                $this->dispatch($attachment->releaseEvents());
            }
        });

        return $submittable;
    }

    /**
     * Exclude invalid attachments e.g. having no file (empty file name), which can easily break later on.
     */
    private function shouldSyncAttachmentFieldValues(array $attachmentFields, $attachment): bool
    {
        return isset($attachmentFields[$attachment->fileId]) && ! empty($attachment?->file?->file);
    }

    public function submittableById(int $submittableId): Submittable
    {
        return $this->submittable->getById($submittableId);
    }

    public function syncContributors(Submittable $submittable, array $contributors, ?int $timestamp = null): array
    {
        $idMap = [];

        if (! empty($contributors)) {
            $this->updateContributors($submittable, $contributors, $timestamp);
        }

        if (! empty($contributors['new'])) {
            $idMap = $this->syncNewContributor($submittable, $contributors['new']);
        }

        return $idMap;
    }

    /**
     * Add in the new contributors.
     *
     * We need to take into account tabs, as the new field incremental counter is unique within each tab, not global.
     * Since each field has it's own tab, we don't care what tab it's on when saving it - this is just to
     * avoid collisions between tabs when creating new fields.
     */
    protected function syncNewContributor(Submittable $submittable, array $contributors): array
    {
        $idMap = [];

        foreach ($contributors as $tabId => $tabs) {
            $idMap[$tabId] = [];

            foreach ($tabs as $id => $fields) {
                if ($id === '-') {
                    continue;
                }

                $idMap[$tabId][$id] = $this->createContributor($submittable, $fields, $tabId);
            }
        }

        return $idMap;
    }

    public static function getInstance(string $type): ?SubmittableService
    {
        if ($type === Form::FORM_TYPE_ENTRY) {
            return app(Entries::class);
        }

        if ($type === Form::FORM_TYPE_REPORT) {
            return app(GrantReports::class);
        }

        return null;
    }

    public function userHasSubmittable(int $userId): bool
    {
        if ($this->submittable->user($userId)->exists()) {
            return true;
        }

        return feature_enabled('collaboration')
            && $this->collaborators->user($userId)
                ->submittableType(get_class($this->submittable->getModel()))
                ->exists();
    }
}
