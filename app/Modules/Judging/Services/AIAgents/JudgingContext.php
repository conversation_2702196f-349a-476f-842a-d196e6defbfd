<?php

namespace AwardForce\Modules\Judging\Services\AIAgents;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\AIAgents\Boundary\Context;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Exceptions\MissingParameterException;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use Illuminate\Container\Attributes\Tag;
use RuntimeException;
use Webmozart\Assert\Assert;

readonly class JudgingContext implements Context
{
    public function __construct(
        private AssignmentRepository $assignments,
        #[Tag(ScoreCalculator::TAG)] private iterable $scoreCalculators,
    ) {
        Assert::allIsInstanceOf($this->scoreCalculators, ScoreCalculator::class);
    }

    public function handle(PromptContext $promptContext, array $metaData): PromptContext
    {
        $entryId = array_get($metaData, 'entry_id')
            ?? throw MissingParameterException::for('entry_id');

        return $promptContext->fill([
            Vertical::replace('judging') => $this->assignments($entryId),
        ]);
    }

    public function applies(array $requiredContexts): bool
    {
        return in_array(AIFieldContext::Judging->value, $requiredContexts, true);
    }

    private function assignments($entryId): array
    {
        $assignments = translate(
            $this->assignments
                ->fields(['id', 'entry_id', 'score_set_id', 'total_votes', 'top_pick_preference'])
                ->with(['scoreSet:id,mode'])
                ->entry($entryId)
                ->status(Assignment::STATUS_COMPLETE)
                ->get()
        )->groupBy('score_set_id');

        return $assignments->map(
            function ($assignments) use ($entryId) {
                $scoreSet = $assignments->first()->scoreSet;

                return [
                    'score_set' => [
                        'name' => $scoreSet->name,
                        'mode' => $scoreSet->mode,
                        'scores' => $this->scores(
                            $assignments,
                            $entryId,
                            $scoreSet->id,
                            $scoreSet->mode
                        ),
                    ],
                ];
            }
        )->values()->toArray();
    }

    private function scores($assignments, $entryId, $scoreSetId, $scoreSetMode): array
    {
        foreach ($this->scoreCalculators as $calculator) {
            if ($calculator->supports($scoreSetMode)) {
                return $calculator->calculate($assignments, $entryId, $scoreSetId);
            }
        }

        throw new RuntimeException("No ScoreCalculator found that supports mode: {$scoreSetMode}");
    }
}
