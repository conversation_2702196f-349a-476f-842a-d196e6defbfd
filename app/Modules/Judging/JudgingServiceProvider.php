<?php

namespace AwardForce\Modules\Judging;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\AIAgents\Services\ContextResolver;
use AwardForce\Modules\Features\Services\Feature;
use AwardForce\Modules\Judging\Commands\RecalculateQualifyingLeaderboard;
use AwardForce\Modules\Judging\Commands\RecalculateTopPickLeaderboard;
use AwardForce\Modules\Judging\Commands\RecalculateVotingLeaderboard;
use AwardForce\Modules\Judging\Data\DecisionRepository;
use AwardForce\Modules\Judging\Data\EloquentDecisionRepository;
use AwardForce\Modules\Judging\Data\EloquentRecusalRepository;
use AwardForce\Modules\Judging\Data\EloquentScoreRepository;
use AwardForce\Modules\Judging\Data\EloquentVotingRepository;
use AwardForce\Modules\Judging\Data\RecusalRepository;
use AwardForce\Modules\Judging\Data\ScoreRepository;
use AwardForce\Modules\Judging\Data\VotingRepository;
use AwardForce\Modules\Judging\Events\RecalculationRequested;
use AwardForce\Modules\Judging\Events\Voting\BallotWasCast;
use AwardForce\Modules\Judging\Listeners\LeaderboardRecalculation;
use AwardForce\Modules\Judging\Listeners\VotingActivity;
use AwardForce\Modules\Judging\Middleware\Agreement;
use AwardForce\Modules\Judging\Middleware\QualifyingAgreementHandler;
use AwardForce\Modules\Judging\Middleware\ScoreSetAgreementHandler;
use AwardForce\Modules\Judging\Middleware\TopPickAgreementHandler;
use AwardForce\Modules\Judging\Middleware\VIPJudgingAgreementHandler;
use AwardForce\Modules\Judging\Services\AIAgents\JudgingContext;
use AwardForce\Modules\Judging\Services\AIAgents\QualifyingScoreCalculator;
use AwardForce\Modules\Judging\Services\AIAgents\ScoreCalculator;
use AwardForce\Modules\Judging\Services\AIAgents\TopPickScoreCalculator;
use AwardForce\Modules\Judging\Services\AIAgents\VipJudgingScoreCalculator;
use AwardForce\Modules\Judging\Services\AIAgents\VotingScoreCalculator;
use AwardForce\Modules\Judging\Services\Modes;
use AwardForce\Modules\Judging\Services\Recalculation\VipJudgingRecalculator;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;

class JudgingServiceProvider extends ServiceProvider
{
    protected $repositories = [
        DecisionRepository::class => EloquentDecisionRepository::class,
        ScoreRepository::class => EloquentScoreRepository::class,
        RecusalRepository::class => EloquentRecusalRepository::class,
        VotingRepository::class => EloquentVotingRepository::class,
    ];
    protected $listeners = [
        BallotWasCast::class => [
            VotingActivity::class.'@processVotes',
            VotingActivity::class.'@increaseCounters',
        ],
        RecalculationRequested::class => [
            LeaderboardRecalculation::class.'@whenRecalculationRequested',
        ],
    ];
    protected $files = [
        __DIR__.'/macros.php',
        __DIR__.'/validators.php',
    ];

    public function register()
    {
        parent::register();

        $this->registerAgreementMiddlewareHandlers();
        $this->registerModesService();
        $this->registerRecalculators();
        $this->registerAIContexts();
    }

    private function registerAgreementMiddlewareHandlers()
    {
        $this->app->scoped(Agreement::class, function () {
            $agreement = new Agreement;

            $agreement->registerHandler(app(ScoreSetAgreementHandler::class));
            $agreement->registerHandler(app(QualifyingAgreementHandler::class));
            $agreement->registerHandler(app(TopPickAgreementHandler::class));
            $agreement->registerHandler(app(VIPJudgingAgreementHandler::class));

            return $agreement;
        });
    }

    private function registerModesService()
    {
        $this->app->scoped(Modes::class, function ($app) {
            return new Modes($app['config']->get('awardforce.judging.modes'), $app->make(Feature::class));
        });
    }

    private function registerRecalculators()
    {
        $this->app->scoped(LeaderboardRecalculation::class, function () {
            $listener = new LeaderboardRecalculation(app(ScoreSetRepository::class));

            $listener->registerRecalculator(RecalculateQualifyingLeaderboard::class);
            $listener->registerRecalculator(RecalculateTopPickLeaderboard::class);
            $listener->registerRecalculator(RecalculateVotingLeaderboard::class);
            $listener->registerRecalculator(VipJudgingRecalculator::class);

            return $listener;
        });
    }

    private function registerAIContexts()
    {
        $this->app->tag(JudgingContext::class, ContextResolver::CONTEXT);

        $this->app->tag(
            [
                VipJudgingScoreCalculator::class,
                VotingScoreCalculator::class,
                TopPickScoreCalculator::class,
                QualifyingScoreCalculator::class,
            ],
            ScoreCalculator::TAG
        );
    }
}
