<?php

namespace AwardForce\Library\Search\Actions;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Platform\Authorisation\FeatureRoles\Entrant;
use Tectonic\LaravelLocalisation\Facades\Translator;

class FormInvitationAction extends SimpleAction
{
    private bool $userMode;
    private array $forms = [];
    private array $roles = [];
    private array $formChapters = [];
    private array $seasonChapters = [];
    private array $categories = [];
    private array $baseData = [];

    public function __construct(string $routeResource, string $permissionResource, bool $userMode = false)
    {
        $this->userMode = $userMode;
        parent::__construct($routeResource, $permissionResource);
    }

    public function action(): string
    {
        return 'update';
    }

    public function viewData($record): array
    {
        return array_merge(parent::viewData($record), $this->composeData($record));
    }

    public function render($record): HtmlString
    {
        return new HtmlString(view('partials.list-actions.form-invitation', $this->viewData($record))->render());
    }

    private function baseData($record)
    {
        if (! empty($this->baseData)) {
            return $this->baseData;
        }
        $chapters = $this->chapters();
        $categories = $this->categories($chapters->pluck('id')->toArray());
        $remembrance = session()->get('invitation_form');

        return $this->baseData = [
            'forms' => $forms = $this->forms($chapters->pluck('id')->toArray()),
            'roles' => $this->roles(),
            'role' => (string) Arr::get($remembrance, 'role'),
            'chapters' => $chapters->map(fn(Chapter $chapter) => [
                'id' => (string) $chapter->id,
                'name' => $chapter->name,
                'forms' => collect($forms)->filter(fn($form) => in_array($chapter->id, $form['chapterIds']))->pluck('id')->toArray(),
            ]),
            'chapter' => (string) ($chapters->count() > 1 ? Arr::get($remembrance, 'chapter') : $chapters->first()?->id),
            'categories' => $categories,
            'category' => (string) Arr::get($remembrance, 'category'),
            'message' => Arr::get($remembrance, 'message-markdown'),
            'deadline' => Arr::get($remembrance, 'deadlineDate'),
            'formId' => (string) (count($forms) > 1 ? Arr::get($remembrance, 'formId') : $forms[0]['id']),
            'showFormSelector' => count($forms) > 1 || feature_enabled('multiform'),
            'route' => route('forms.invite'),
            'labels' => [
                'button' => $this->userMode ? trans_elliptic('form.buttons.invite-entrant') : trans_elliptic('form.buttons.invite-entrants'),
                'cancel' => trans('buttons.cancel'),
                'title' => $this->userMode ? trans('form.buttons.invite-entrant') : trans('form.buttons.invite-entrants'),
                'chapterSelector' => trans_merge(':chapter', 'miscellaneous.optional'),
                'categorySelector' => trans_merge(':category', 'miscellaneous.optional'),
                'message' => trans_merge('shared.message', 'miscellaneous.optional'),
                'deadline' => trans_merge('entries.titles.deadline', 'miscellaneous.optional'),
                'formSelector' => trans_merge('form.selector.label'),
                'roleSelector' => trans_merge('shared.resources.role'),
                'notifications' => trans_merge('notifications.titles.main', 'miscellaneous.optional'),
            ],
            'translations' => self::translations(),
            'invitedEntryNotifications' => $this->invitedEntryNotifications($record),
        ];
    }

    private function composeData($record): array
    {
        $data = $this->baseData($record);
        if ($this->userMode) {
            $data['user'] = $record;
            if (! isset($data['user']['full_name'])) {
                $data['user']['full_name'] = $data['user']->fullName();
            }
        } else {
            $data['chapterOption'] = $record->chapterOption;
        }

        return $data + [
            'selected' => $record->id,
            'record' => [],
        ];
    }

    private function roles()
    {
        if (! empty($this->roles)) {
            return $this->roles;
        }

        $userLevel = \Consumer::user()->highestRolePermissionLevel();

        return $this->roles = Translator::shallow(
            app(RoleRepository::class)
                ->getAllWithAnyPermission(Entrant::permissions())
                ->filter(fn(Role $role) => $userLevel > $role->permissionLevel())
        )
            ->map(fn(Role $role) => ['id' => (string) $role->slug, 'name' => $role->name])
            ->values()
            ->toArray();
    }

    private function chapters()
    {
        $seasonId = SeasonFilter::viewingAll() ? null : SeasonFilter::getId();

        return $this->seasonChapters[$seasonId] ?? $this->seasonChapters[$seasonId] = Translator::shallow(app(ChapterRepository::class)->getActive($seasonId));
    }

    private function categories(array $chapterIds): array
    {
        return $this->categories ?: $this->categories = app(CategoryRepository::class)
            ->requestCache()
            ->leaves()
            ->chapters($chapterIds)
            ->listChapterIds()
            ->fields(['categories.id', 'categories.active', 'categories.form_id'])
            ->get()
            ->shallow()
            ->map(fn(Category $category) => [
                'id' => (string) $category->id,
                'name' => $category->active ? $category->name : trans('entries.form.category.selector.inactive', ['category' => $category->name]),
                'chapters' => explode(',', $category->listChapterIds),
                'formId' => $category->formId,
            ])
            ->values()
            ->toArray();
    }

    private function invitedEntryNotifications($record)
    {
        return Translator::shallow(app(NotificationRepository::class)->getByTrigger('entry.invited', $record->seasonId ?: SeasonFilter::getId()))
            ->map(fn(Notification $notification) => [
                'id' => $notification->id,
                'subject' => $notification->fullSubject(),
            ])
            ->values()
            ->toArray();
    }

    private function forms(array $chapterIds)
    {
        $season = SeasonFilter::viewingAll() ? null : SeasonFilter::get();

        $this->forms ?: $this->forms = Translator::shallow(app(FormRepository::class)
            ->selectedSeason($season?->id)
            ->forType(Form::FORM_TYPE_ENTRY)
            ->listChapterIds()
            ->fields(['forms.id', 'forms.chapter_option'])
            ->get()
        )
            ->map(fn(Form $form) => [
                'id' => (string) $form->id,
                'name' => $form->name,
                'chapterOption' => $form->chapterOption,
                'chapterIds' => $form->appliesToAllChapters() ? $chapterIds : array_map('intval', explode(',', $form->listChapterIds)),
            ])
            ->values()
            ->toArray();

        return $this->forms;
    }

    public static function translationKeys(): array
    {
        return [
            'form.form.name.label',
            'form.messages.multiple_emails',
            'miscellaneous.datepicker.tooltips',
            'miscellaneous.markdown_guide',
            'notifications.missing',
            'notifications.triggers.entry_invited',
            'validation.attributes.users-left',
            'validation.multiple_emails',
            'users.form.email.label',
        ];
    }

    public static function translations(): array
    {
        return translations_for_vue(Consumer::languageCode(), self::translationKeys());
    }
}
