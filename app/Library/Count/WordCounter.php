<?php

namespace AwardForce\Library\Count;

class WordCounter
{
    /**
     * Count the number of words in a given content. Closest 1:1 clone of frontend's solution
     * to avoid validation issues with backend/frontend returning distinct word counts.
     *
     * Original frontend's solution: Counter.ts
     */
    public static function count(string $content): int
    {
        $content = self::normalizeHtmlContent($content);

        // Check if the content contains any Chinese and Japanese ideographs.
        $hasIdeographs = self::stringHasIdeographs($content);

        // Replace URLs with a placeholder to avoid counting their parts as separate words.
        $contentWithUrlPlaceholders = self::replaceUrlsWithPlaceholder($content);

        return $hasIdeographs
            ? self::countWordsWithIdeographs($contentWithUrlPlaceholders)
            : self::countWordsWithoutIdeographs($contentWithUrlPlaceholders);
    }

    private static function normalizeHtmlContent(string $content): string
    {
        // Add space after closing tags to avoid word concatenation
        // ⚠️IMPORTANT: Keep this array in sync with the frontend's `Counter.ts` file
        $tagsToPad = ['</p>', '</figcaption>', '</li>', '<br>'];
        foreach ($tagsToPad as $tag) {
            $content = str_ireplace($tag, $tag.' ', $content);
        }

        $content = strip_tags($content);

        // Decode HTML entities eg.: `&amp;` to `&`
        return html_entity_decode($content, ENT_QUOTES | ENT_HTML5);
    }

    private static function chineseJapaneseKoreanThaiCharactersRegex(): string
    {
        // This could be used, but our server's PCRE library is too old and doesn't support this notation
        //return '(?=[\p{Script=Han}\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Thai}\p{Script=Hangul}])';

        return '(?=[\x{3040}-\x{30FF}\x{3400}-\x{4DBF}\x{4E00}-\x{9FFF}\x{F900}-\x{FAFF}\x{FF66}-\x{FF9F}\x{0E00}-\x{0E7F}\x{AC00}-\x{D7AF}])';
    }

    private static function stringHasIdeographs(string $content): bool
    {
        return preg_match(
            '/[\x{3040}-\x{30ff}\x{3400}-\x{4dbf}\x{4e00}-\x{9fff}\x{f900}-\x{faff}\x{ff66}-\x{ff9f}]/u',
            $content
        ) === 1;
    }

    private static function replaceUrlsWithPlaceholder(string $content): string
    {
        $urlPattern = '/(https?:\/\/)?([a-zA-Z0-9-]+\.)?([a-zA-Z0-9-]+\.[a-zA-Z]{2,})(\/?[^\s]*)?/i';

        return preg_replace($urlPattern, 'URL_PLACEHOLDER', $content) ?? $content;
    }

    private static function countWordsWithoutIdeographs(string $contentWithUrlPlaceholders): int
    {
        // IntlBreakIterator is PHP's equivalent of JS 'Intl.Segmenter' - a word break iterator
        $segmenter = \IntlBreakIterator::createWordInstance();

        $cleanedContent = str_replace(['&nbsp;', '-'], [' ', ''], $contentWithUrlPlaceholders);
        $segmenter->setText($cleanedContent);

        $wordCounter = 0;

        // getPartsIterator() yields all text segments (words, punctuation, whitespace).
        foreach ($segmenter->getPartsIterator() as $segment) {

            // Replicate js `segment.isWordLike` by checking if the segment contains
            // Unicode letter (\p{L}) or number (\p{N}).
            if (preg_match('/[\p{L}\p{N}]/u', $segment)) {
                $wordCounter++;
            }
        }

        return $wordCounter;
    }

    private static function countWordsWithIdeographs(string $contentWithUrlPlaceholders): ?int
    {
        // Use regex to split the string into words
        $patterns = [
            // Non-breaking spaces.
            '&nbsp;',
            // Punctuation and space characters when surrounded by whitespace or at string edges
            // Unicode ranges for General and Supplemental Punctuation are included.
            '(^|\s+)[!\'"#$%&()*+,.\/\\\\:;<=>?@\[\]^_`{|}~«»\x{2000}-\x{206F}\x{2E00}-\x{2E7F}\s]+(\s+|$)',
            // Match any remaining whitespace.
            '\s+',
            // Match the boundary before a CJK, Thai, or Korean character.
            // This is a positive lookahead, so it splits without consuming the character.
            self::chineseJapaneseKoreanThaiCharactersRegex(),
        ];

        $regex = '/'.implode('|', $patterns).'/u';
        $words = preg_split($regex, $contentWithUrlPlaceholders);

        // Filter out empty words
        $filteredWords = array_filter($words, fn($value) => $value !== null && trim($value) !== '');

        return count($filteredWords);
    }
}
