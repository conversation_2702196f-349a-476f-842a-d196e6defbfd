<?php

namespace AwardForce\Library\Count;

use Tests\BaseTestCase;

/**
 * This is a clone of frontend's tests in: js/src/domain/utils/Counter.spec.ts
 */
class WordCounterTest extends BaseTestCase
{
    public function testReturnsCorrectWordCountForSimpleSentence(): void
    {
        $content = 'Hello world';

        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testReturnsZeroForEmptyString(): void
    {
        $content = '';

        $this->assertEquals(0, WordCounter::count($content));
    }

    public function testItExcludesHtml(): void
    {
        $content = '<p>Text <strong>in</strong><font style="color: red">side</font></p>' // 2 words
            .'<p>Foo <i>bar</i></p>'; // Also 2 words

        $this->assertEquals(4, WordCounter::count($content));
    }

    public function testCountsWordsInFiguresCaptionCorrectly(): void
    {
        $content = '<figure class="image"><img src="src" alt="alt"><figcaption>One Two</figcaption></figure><p>Three</p>';
        $this->assertEquals(3, WordCounter::count($content));
    }

    public function testCountsWordsInListItemsCorrectly(): void
    {
        $content = '<ul><li>One</li><li>Two</li><li>Three</li></ul>';
        $this->assertEquals(3, WordCounter::count($content));
    }

    public function testHandlesWordCountWithBrTagsProperly(): void
    {
        $content = 'Hello<br>world';
        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testDoesNotCountAmpAsAWord(): void
    {
        $content = 'Hello &amp; world';
        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testDoesNotCountAmpersandAsAWord(): void
    {
        $content = 'Hello & world';
        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithPunctuation(): void
    {
        $content = 'Hello, world!';

        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithMultipleSpaces(): void
    {
        $content = 'Hello   world';

        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithNonBreakingSpaces(): void
    {
        $content = 'Hello&nbsp;new&nbsp;world';
        $this->assertEquals(3, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithSpecialCharacters(): void
    {
        $content = 'Hello @world!';

        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithLessThanAndGreaterThanSigns(): void
    {
        $content = '<p>One &lt;two &gt;three</p>';

        $this->assertEquals(3, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithMixedLanguages(): void
    {
        $content = 'Hello 世界';

        $this->assertEquals(3, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithHyphenatedWords(): void
    {
        $content = 'state-of-the-art';

        $this->assertEquals(1, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithNewlineCharacters(): void
    {
        $content = "Hello\nworld";

        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithChineseCharacters(): void
    {
        $content = '你好世界';

        $this->assertEquals(4, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithJapaneseCharacters(): void
    {
        $content = 'こんにちは世界';

        $this->assertEquals(7, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithThaiCharacters(): void
    {
        $content = 'การตอบกลับอีเมลต้องเป็นที่อยู่อีเมลที่ถูกต้องหรือหมายเลขโทรศัพท์มือถือในรูปแบบสากล';

        $this->assertEquals(21, WordCounter::count($content));
    }

    public function testCountsWordsCorrectlyWithKoreanCharacters(): void
    {
        $content = '안녕하세요 세계';

        $this->assertEquals(2, WordCounter::count($content));
    }

    public function testCountsUrlsAsSingleWord(): void
    {
        $urls = [
            'https://jedi.force.dev/entry-form/manager/xJZWVOpA/edit?tabSlug=rYVaPVrl',
            'jedi.force.dev/entry-form/manager/xJZWVOpA/edit?tabSlug=rYVaPVrl',
            'www.force.dev',
            'force.dev',
        ];

        foreach ($urls as $content) {
            $this->assertEquals(1, WordCounter::count($content));
        }
    }
}
