<?php

namespace AwardForce\Library\Identifier\Snowflake;

use Assert\Assertion;
use Eloquence\Behaviours\Slug;
use Illuminate\Contracts\Database\Eloquent\Castable;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Ramsey\Identifier\Snowflake\GenericSnowflake;
use Ramsey\Identifier\Snowflake\Utility\Standard;

/**
 * Based on \Ramsey\Identifier\Snowflake\DiscordSnowflake
 */
class SnowflakeId implements Castable, Snowflake
{
    use Standard;

    final public function __construct(int|string $snowflake)
    {
        $this->snowflake = new GenericSnowflake($snowflake, Epoch::TheForce->value);
    }

    public function toSlug(): Slug
    {
        return Slug::fromId($this->snowflake->toInteger());
    }

    public static function castUsing(array $attributes): CastsAttributes
    {
        return new class implements CastsAttributes
        {
            public function get($model, $key, $value, $attributes): SnowflakeId
            {
                $type = $model->getCasts()[$key];

                return new $type($value);
            }

            public function set($model, $key, $value, $attributes): array
            {
                Assertion::isInstanceOf($value, SnowflakeId::class);

                return [$key => $value->toInteger()];
            }
        };
    }
}
