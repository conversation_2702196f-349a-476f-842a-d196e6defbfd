<?php

namespace AwardForce\Library\Identifier\Snowflake;

use Brick\Math\BigInteger;
use DateTimeInterface;
use Psr\Clock\ClockInterface;
use Ramsey\Identifier\Exception\InvalidArgument;
use Ramsey\Identifier\Service\Clock\Precision;
use Ramsey\Identifier\Service\Clock\Sequence;
use Ramsey\Identifier\Service\Clock\StatefulSequence;
use Ramsey\Identifier\Service\Clock\SystemClock;
use Ramsey\Identifier\Snowflake\Utility\StandardFactory;

/**
 * Based on \Ramsey\Identifier\Snowflake\DiscordSnowflakeFactory
 */
class Snowflakes implements \Ramsey\Identifier\SnowflakeFactory
{
    use StandardFactory;

    /**
     * For performance, we'll prepare the worker and process Id bits and store
     * them for repeated use.
     */
    private readonly int $workerProcessIdShifted;

    /**
     * Constructs a factory for creating TheForce Snowflakes
     *
     * @param  int<0, 31>  $workerId A 5-bit worker identifier to use when
     *     creating Snowflakes
     * @param  int<0, 31>  $processId A 5-bit process identifier to use when
     *     creating Snowflakes
     * @param  ClockInterface  $clock A clock used to provide a date-time instance;
     *     defaults to {@see SystemClock}
     * @param  Sequence  $sequence A sequence that provides a clock sequence value
     *     to prevent collisions; defaults to {@see StatefulSequence} with
     *     millisecond precision
     */
    public function __construct(
        private readonly int $workerId,
        private readonly int $processId,
        private readonly ClockInterface $clock = new SystemClock(),
        private readonly Sequence $sequence = new StatefulSequence(precision: Precision::Millisecond),
    ) {
        $this->workerProcessIdShifted = ($this->workerId & 0x1F) << 17 | ($this->processId & 0x1F) << 12;
    }

    /**
     * @throws InvalidArgument
     */
    public function create(): SnowflakeId
    {
        return $this->createFromDateTime($this->clock->now());
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromBytes(string $identifier): SnowflakeId
    {
        return new SnowflakeId($this->convertFromBytes($identifier));
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromDateTime(DateTimeInterface $dateTime): SnowflakeId
    {
        $milliseconds = (int) $dateTime->format('Uv') - Epoch::TheForce->value;

        if ($milliseconds < 0) {
            throw new InvalidArgument(sprintf(
                'Timestamp may not be earlier than the TheForce epoch, %s',
                Epoch::TheForce->toIso8601(),
            ));
        }

        $sequence = $this->sequence->value($this->workerId + $this->processId, $dateTime) & 0x0FFF;

        $millisecondsShifted = $milliseconds << 22;

        if ($millisecondsShifted > $milliseconds) {
            $identifier = $millisecondsShifted | $this->workerProcessIdShifted | $sequence;
        } else {
            /** @var numeric-string $identifier */
            $identifier = (string) BigInteger::of($milliseconds)
                ->shiftedLeft(22)
                ->or($this->workerProcessIdShifted)
                ->or($sequence);
        }

        return new SnowflakeId($identifier);
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromHexadecimal(string $identifier): SnowflakeId
    {
        return new SnowflakeId($this->convertFromHexadecimal($identifier));
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromInteger(int|string $identifier): SnowflakeId
    {
        return new SnowflakeId($identifier);
    }

    /**
     * @throws InvalidArgument
     */
    public function createFromString(string $identifier): SnowflakeId
    {
        /** @var numeric-string $value */
        $value = $identifier;

        return new SnowflakeId($value);
    }
}
