<?php

namespace AwardForce\Library\Database\Node;

use Illuminate\Database\Connection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\Builder as BaseQueryBuilder;
use Illuminate\Database\Query\Expression;
use Illuminate\Database\Query\Grammars\Grammar;
use Illuminate\Database\Query\Processors\Processor;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class NodeEloquentBuilderTest extends BaseTestCase
{
    use Laravel;

    public function testItSkipLockedWhenLockingForUpdate()
    {
        $dummyModel = new DummyNodeModel;
        $builder = $dummyModel->newQuery();

        $builder->where('left', '>', 0);
        $builder->lockForUpdate();

        $this->assertStringContainsString('FOR UPDATE SKIP LOCKED', $builder->toSql());
        $this->assertStringContainsString('left', $builder->toSql());
        $this->assertStringContainsString('season_id', $builder->toSql());
    }

    public function testUpdateAppliesForceIndexAndTimestamps()
    {
        $connection = m::mock(Connection::class);
        $connection->shouldReceive('update')->once()->andReturn(1);
        $grammar = m::mock(Grammar::class);
        $grammar->shouldReceive('compileUpdate')->once()->withArgs(function (Builder $query, array $values) {
            $this->assertArrayHasKey('left', $values);
            $this->assertArrayHasKey('updated_at', $values);
            $this->assertInstanceOf(\DateTime::class, $values['updated_at']);

            return true;
        });
        $grammar->shouldReceive('prepareBindingsForUpdate')->once()->andReturn([]);
        $processor = m::mock(Processor::class);

        $baseQuery = new BaseQueryBuilder($connection, $grammar, $processor);
        $model = new DummyNodeModel;
        $fromString = $model->getTable().' FORCE INDEX ('.$model->getTable().'_'.implode('_', $model->getScopedColumns()).'_index)';

        $builder = m::mock(NodeEloquentBuilder::class.'[performUpdate]', [$baseQuery])->shouldAllowMockingProtectedMethods();
        $builder->setModel($model);

        $result = $builder->update(['left' => 90]);

        $this->assertEquals(1, $result);
        $this->assertTrue($model->timestamps); // timestamps were re-enabled

        $this->assertInstanceOf(Expression::class, $baseQuery->from);
        $this->assertEquals($fromString, (string) $baseQuery->from->getValue($grammar));
    }
}

class DummyNodeModel extends Node
{
    public $timestamps = true;
    protected $table = 'dummy_nodes';
    protected $scoped = ['season_id'];

    public function shouldForceIndexOnUpdate(): bool
    {
        return true;
    }

    public function getLeftColumnName(): string
    {
        return 'left';
    }

    public function getRightColumnName(): string
    {
        return 'right';
    }
}
