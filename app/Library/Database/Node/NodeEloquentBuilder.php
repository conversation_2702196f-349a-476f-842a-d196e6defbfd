<?php

namespace AwardForce\Library\Database\Node;

use Platform\Database\Eloquent\Builder;

class NodeEloquentBuilder extends Builder
{
    public function lockForUpdate()
    {
        return $this->lock('FOR UPDATE SKIP LOCKED')->scoped();
    }

    public function update(array $values)
    {
        $currentTimestamps = $this->model->timestamps;
        $values = $this->addUpdatedAt($values);
        // Temporarily disable timestamps because using a raw `from` clause with `FORCE INDEX`
        // prevents Eloquent from resolving the table name correctly for automatic `updated_at` handling.
        $this->model->timestamps = false;

        if ($this->model->shouldForceIndexOnUpdate()) {
            $this->query->from(\DB::raw($this->query->from.' FORCE INDEX ('.$this->scopedIndex().')'));
        }

        return tap(parent::update($values), fn() => $this->model->timestamps = $currentTimestamps);
    }

    private function addUpdatedAt(array $values): array
    {
        if ($this->model->usesTimestamps() && ! array_key_exists($this->model->getUpdatedAtColumn(), $values)) {
            $values[$this->model->getUpdatedAtColumn()] = now();
        }

        return $values;
    }

    private function scopedIndex(): string
    {
        return $this->query->from.'_'.implode('_', $this->model->getScopedColumns()).'_index';
    }
}
