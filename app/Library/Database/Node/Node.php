<?php

namespace AwardForce\Library\Database\Node;

use AwardForce\Library\Database\Eloquent\HasToJson;
use Illuminate\Database\Eloquent\Builder;

abstract class Node extends \Baum\Node
{
    use HasToJson;

    public function newEloquentBuilder($query)
    {
        return new NodeEloquentBuilder($query);
    }

    /**
     * Sets default values for left and right fields.
     * This method is almost the same as the one in the parent class, but it uses withTrashed() method.
     */
    public function setDefaultLeftAndRight(): void
    {
        $query = $this->newNestedSetQuery();
        if ($this->areSoftDeletesEnabled()) {
            $query->withTrashed();
        }

        $withHighestRight = $query->reOrderBy($this->getRightColumnName(), 'desc')->take(1)->sharedLock()->first();

        $maxRgt = 0;
        if (! is_null($withHighestRight)) {
            $maxRgt = $withHighestRight->getRight();
        }

        $this->setAttribute($this->getLeftColumnName(), $maxRgt + 1);
        $this->setAttribute($this->getRightColumnName(), $maxRgt + 2);
    }

    public function scopeScoped(Builder $query)
    {
        if ($this->isScoped()) {
            foreach ($this->getScopedColumns() as $scopeField) {
                $query->where($scopeField, $this->$scopeField);
            }
        }
    }

    public function shouldForceIndexOnUpdate(): bool
    {
        return false;
    }
}
