<?php

namespace AwardForce\Library\Filesystem;

use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class MetadataTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testCamera(): void
    {
        $metadata = Metadata::fromServiceData(['TIFF' => ['Make' => 'Pentax', 'Model' => 'Spotmatic']]);

        $this->assertEquals('Pentax Spotmatic', (string) $metadata->camera);

        $metadata = Metadata::fromServiceData(['TIFF' => ['Make' => 'Pentax', 'Model' => 'Pentax Spotmatic']]);
        $this->assertEquals('Pentax Spotmatic', (string) $metadata->camera);

        $metadata = Metadata::fromServiceData(['TIFF' => []]);
        $this->assertEmpty((string) $metadata->camera);
    }

    public function testExposure(): void
    {
        $metadata = Metadata::fromServiceData(['Exif' => ['ExposureTime' => 0.0004]]);
        $this->assertEquals('1/2500 sec', (string) $metadata->exposure);

        $metadata = Metadata::fromServiceData(['Exif' => ['ExposureTime' => 0.00042]]);
        $this->assertEquals('0.00042 sec', (string) $metadata->exposure);

        $metadata = Metadata::fromServiceData(['Exif' => ['ExposureTime' => 15]]);
        $this->assertEquals('15 sec', (string) $metadata->exposure);

        $metadata = Metadata::fromServiceData(['Exif' => ['ExposureTime' => 5.0E-5]]);
        $this->assertEquals('1/20000 sec', (string) $metadata->exposure);

        $metadata = Metadata::fromServiceData(['Exif' => ['ExposureTime' => 0.00005]]);
        $this->assertEquals('1/20000 sec', (string) $metadata->exposure);

        $metadata = Metadata::fromServiceData(['Exif' => ['ExposureTime' => 0.0000001]]);
        $this->assertEquals('1/10000000 sec', (string) $metadata->exposure);

        $metadata = Metadata::fromServiceData(['Exif' => ['ExposureTime' => [0, 1.5]]]);
        $this->assertEquals('', (string) $metadata->exposure);

        $metadata = Metadata::fromServiceData([]);
        $this->assertEmpty((string) $metadata->exposure);
    }

    public function testFStop(): void
    {
        $metadata = Metadata::fromServiceData(['Exif' => ['FNumber' => 2.001]]);
        $this->assertEquals('f/2.0', (string) $metadata->fStop);

        $metadata = Metadata::fromServiceData(['Exif' => ['FNumber' => [53, 2]]]);
        $this->assertEquals('f/26.5', (string) $metadata->fStop);

        $metadata = Metadata::fromServiceData([]);
        $this->assertEmpty((string) $metadata->fStop);
    }

    public function testSize(): void
    {
        $metadata = Metadata::fromServiceData(['PixelWidth' => '2000', 'PixelHeight' => '4000', 'DPIHeight' => '300']);
        $this->assertEquals('2000 x 4000 (300 dpi)', (string) $metadata->size);

        $metadata = Metadata::fromServiceData(['PixelWidth' => '3000', 'PixelHeight' => '5000', 'DPIHeight' => '']);
        $this->assertEquals('3000 x 5000', (string) $metadata->size);

        $metadata = Metadata::fromServiceData(['PixelWidth' => '', 'PixelHeight' => '', 'DPIHeight' => '300']);
        $this->assertEquals('300 dpi', (string) $metadata->size);

        $metadata = Metadata::fromServiceData(['PixelWidth' => '', 'PixelHeight' => '', 'DPIHeight' => '']);
        $this->assertEmpty((string) $metadata->size);
    }

    public function testIso(): void
    {
        $metadata = Metadata::fromServiceData(['Exif' => ['ISOSpeedRatings' => [100, 200, 400]]]);
        $this->assertEquals('100, 200, 400', (string) $metadata->iso);

        $metadata = Metadata::fromServiceData(['Exif' => ['ISOSpeedRatings' => 100]]);
        $this->assertEquals('100', (string) $metadata->iso);

        $metadata = Metadata::fromServiceData(['Exif' => ['ISOSpeedRatings' => []]]);
        $this->assertEmpty((string) $metadata->iso);

        $metadata = Metadata::fromServiceData([]);
        $this->assertEmpty((string) $metadata->iso);

        $metadata = Metadata::fromServiceData(['Exif' => ['ISOSpeedRatings' => null]]);
        $this->assertEmpty((string) $metadata->iso);
    }

    public function testTitle(): void
    {
        $metadata = Metadata::fromServiceData(['IPTC' => ['ObjectName' => 'Test Title']]);
        $this->assertEquals('Test Title', $metadata->title);
    }

    public function testCaption(): void
    {
        $metadata = Metadata::fromServiceData(['IPTC' => ['Caption/Abstract' => 'Test Caption']]);
        $this->assertEquals('Test Caption', $metadata->caption);
    }

    public function testCopyright(): void
    {
        $metadata = Metadata::fromServiceData(['IPTC' => ['CopyrightNotice' => 'Test Copyright']]);
        $this->assertEquals('Test Copyright', $metadata->copyright);
    }

    public function testCreated(): void
    {
        $metadata = Metadata::fromServiceData(['Exif' => ['DateTimeOriginal' => 'Test Date']]);
        $this->assertEquals('Test Date', $metadata->created);
    }

    public function testCity(): void
    {
        $metadata = Metadata::fromServiceData(['IPTC' => ['City' => 'Test City']]);
        $this->assertEquals('Test City', $metadata->city);
    }

    public function testCountry(): void
    {
        $metadata = Metadata::fromServiceData(['IPTC' => ['Country/PrimaryLocationName' => 'Test Country']]);
        $this->assertEquals('Test Country', $metadata->country);
    }

    public function testState(): void
    {
        $metadata = Metadata::fromServiceData(['IPTC' => ['Province/State' => 'Test State']]);
        $this->assertEquals('Test State', $metadata->state);
    }

    public function testFocalLength(): void
    {
        $metadata = Metadata::fromServiceData(['Exif' => ['FocalLength' => 'Test FocalLength']]);
        $this->assertEquals('Test FocalLength mm', $metadata->focalLength);

        $metadata = Metadata::fromServiceData(['Exif' => ['FocalLength' => [53, 5.7]]]);
        $this->assertEquals(53 / 5.7.' mm', (string) $metadata->focalLength);
    }

    public function testImageWidth(): void
    {
        $metadata = Metadata::fromServiceData(['PixelWidth' => 1000]);
        $this->assertEquals(1000, $metadata->imageWidth);
    }

    public function testImageHeight(): void
    {
        $metadata = Metadata::fromServiceData(['PixelHeight' => 2000]);
        $this->assertEquals(2000, $metadata->imageHeight);
    }

    public function testFields(): void
    {
        $metadata = Metadata::fromServiceData([
            'TIFF' => ['Make' => 'Pentax', 'Model' => 'Spotmatic'],
            'Exif' => ['FNumber' => 2.001],
            'DPIHeight' => '300',
        ]);

        $fields = $metadata->fields();
        $this->assertCount(3, $fields);
        $this->assertArrayHasKey('camera', $fields->toArray());
        $this->assertArrayHasKey('fStop', $fields->toArray());
        $this->assertArrayHasKey('size', $fields->toArray());
    }

    public function testMetadataIsEmpty()
    {
        $metadata = Metadata::fromServiceData([]);

        $this->assertTrue($metadata->empty());
    }
}
