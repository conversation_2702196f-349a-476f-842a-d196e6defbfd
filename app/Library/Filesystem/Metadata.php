<?php

namespace AwardForce\Library\Filesystem;

use Assert\Assertion;
use AwardForce\Library\Values\Services\Transformable;
use AwardForce\Library\Values\Services\Transformer;
use AwardForce\Modules\Files\Services\ExifParser;
use AwardForce\Modules\Files\Values\Camera;
use AwardForce\Modules\Files\Values\Exposure;
use AwardForce\Modules\Files\Values\FocalLength;
use AwardForce\Modules\Files\Values\FStop;
use AwardForce\Modules\Files\Values\Iso;
use AwardForce\Modules\Files\Values\Size;
use Illuminate\Contracts\Database\Eloquent\Castable;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

readonly class Metadata implements Arrayable, Castable, Transformable
{
    use Transformer;

    public function __construct(
        public ?string $title = null,
        public ?string $caption = null,
        public ?string $copyright = null,
        public ?string $created = null,
        public ?string $city = null,
        public ?string $country = null,
        public ?string $state = null,
        public Camera $camera = new Camera,
        public Exposure $exposure = new Exposure,
        public Iso $iso = new Iso,
        public FStop $fStop = new FStop,
        public FocalLength $focalLength = new FocalLength,
        public Size $size = new Size,
        public ?int $imageWidth = null,
        public ?int $imageHeight = null
    ) {
    }

    public static function fromServiceData(array $metadata): self
    {
        $exifMetadata = new ExifParser($metadata);

        return new self(
            Arr::get($metadata, 'IPTC.ObjectName'),
            Arr::get($metadata, 'IPTC.Caption/Abstract'),
            Arr::get($metadata, 'IPTC.CopyrightNotice'),
            $exifMetadata->datetime(),
            Arr::get($metadata, 'IPTC.City'),
            Arr::get($metadata, 'IPTC.Country/PrimaryLocationName'),
            Arr::get($metadata, 'IPTC.Province/State'),
            new Camera(Arr::get($metadata, 'TIFF.Make'), Arr::get($metadata, 'TIFF.Model')),
            $exifMetadata->exposure(),
            $exifMetadata->iso(),
            $exifMetadata->fStop(),
            $exifMetadata->focalLength(),
            new Size(Arr::get($metadata, 'PixelWidth'), Arr::get($metadata, 'PixelHeight'), Arr::get($metadata, 'DPIHeight')),
            (int) Arr::get($metadata, 'PixelWidth'),
            (int) Arr::get($metadata, 'PixelHeight')
        );
    }

    public static function castUsing(array $arguments): CastsAttributes
    {
        return new class implements CastsAttributes
        {
            public function get($model, string $key, $value, array $attributes): Metadata
            {
                $data = json_decode($value ?? '{}', true);

                return Metadata::transform($data);
            }

            public function set($model, string $key, $value, array $attributes): string
            {
                Assertion::isInstanceOf($value, Metadata::class);

                return json_encode($value->toArray());
            }
        };
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }

    public function fields(): Collection
    {
        return collect(get_object_vars($this))
            ->filter(fn($value) => (string) $value);
    }

    public function empty(): bool
    {
        return $this->fields()->isEmpty();
    }
}
