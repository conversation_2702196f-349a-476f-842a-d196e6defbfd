FROM 481530657064.dkr.ecr.eu-central-1.amazonaws.com/infra:af4-the-force-8-3

USER root

RUN mkdir -p /var/www/af4 && \
    cd /var/www/af4 && \
    mkdir -p storage/framework/views \
             storage/logs \
             storage/app/exports \
             bootstrap/cache && \
    chmod -R 0770 storage bootstrap && \
    chown -R www-data:www-data /var/www

COPY --chown=www-data:www-data . /var/www/af4

RUN cd /datadog && php datadog-setup.php --php-bin=all

USER www-data

ENTRYPOINT ["/entrypoint.sh"]

CMD ["php-fpm"]
